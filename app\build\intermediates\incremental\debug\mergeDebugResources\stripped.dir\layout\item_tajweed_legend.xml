<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="8dp"
    app:cardBackgroundColor="@color/background_secondary"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:gravity="center_vertical">

        <!-- Color Indicator -->
        <View
            android:id="@+id/view_color_indicator"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginEnd="16dp"
            android:background="@drawable/circle_background"
            tools:backgroundTint="#01A1DB" />

        <!-- Text Content -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <!-- Rule Names -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <TextView
                    android:id="@+id/text_rule_name_arabic"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/text_primary"
                    android:textDirection="rtl"
                    android:textSize="18sp"
                    tools:text="غُنَّة" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="8dp"
                    android:text="•"
                    android:textColor="@color/text_secondary"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/text_rule_name_english"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="sans-serif-medium"
                    android:textColor="@color/text_primary"
                    android:textSize="16sp"
                    tools:text="Ghunna" />

            </LinearLayout>

            <!-- Example -->
            <TextView
                android:id="@+id/text_example"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:textDirection="rtl"
                android:textSize="20sp"
                tools:text="مِنْ"
                tools:textColor="#01A1DB" />

            <!-- Explanation -->
            <TextView
                android:id="@+id/text_explanation"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:fontFamily="sans-serif"
                android:textColor="@color/text_secondary"
                android:textSize="14sp"
                tools:text="Nasal sound with Noon or Meem (2 counts)" />

        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView>
