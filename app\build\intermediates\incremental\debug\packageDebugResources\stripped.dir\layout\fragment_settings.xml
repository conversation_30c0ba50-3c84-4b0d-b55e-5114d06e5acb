<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Header -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardBackgroundColor="@color/islamic_green_primary"
            app:cardCornerRadius="20dp"
            app:cardElevation="8dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="24dp">

                <TextView
                    android:id="@+id/text_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:text="@string/nav_settings"
                    android:textColor="@color/text_on_primary"
                    android:textSize="24sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/text_subtitle"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:gravity="center"
                    android:text="Customize your Islamic app experience"
                    android:textColor="@color/text_on_primary"
                    android:textSize="16sp" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- App Settings -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="16dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:text="App Settings"
                    android:textColor="@color/text_primary"
                    android:textSize="18sp"
                    android:textStyle="bold" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/button_language"
                    style="@style/Widget.Material3.Button.OutlinedButton"
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:layout_marginBottom="12dp"
                    android:text="@string/language"
                    android:textAllCaps="false"
                    app:icon="@drawable/ic_settings"
                    app:strokeColor="@color/islamic_green_primary" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/button_theme"
                    style="@style/Widget.Material3.Button.OutlinedButton"
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:layout_marginBottom="12dp"
                    android:text="@string/theme"
                    android:textAllCaps="false"
                    app:icon="@drawable/ic_settings"
                    app:strokeColor="@color/islamic_green_primary" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/button_notifications"
                    style="@style/Widget.Material3.Button.OutlinedButton"
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:layout_marginBottom="12dp"
                    android:text="@string/notifications"
                    android:textAllCaps="false"
                    app:icon="@drawable/ic_settings"
                    app:strokeColor="@color/islamic_green_primary" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/button_location"
                    style="@style/Widget.Material3.Button.OutlinedButton"
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:text="@string/location"
                    android:textAllCaps="false"
                    app:icon="@drawable/ic_location"
                    app:strokeColor="@color/islamic_green_primary" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- About & Support -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="16dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:text="About &amp; Support"
                    android:textColor="@color/text_primary"
                    android:textSize="18sp"
                    android:textStyle="bold" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/button_privacy"
                    style="@style/Widget.Material3.Button.OutlinedButton"
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:layout_marginBottom="12dp"
                    android:text="@string/privacy_policy"
                    android:textAllCaps="false"
                    app:icon="@drawable/ic_settings"
                    app:strokeColor="@color/islamic_green_primary" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/button_feedback"
                    style="@style/Widget.Material3.Button.OutlinedButton"
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:layout_marginBottom="12dp"
                    android:text="@string/feedback"
                    android:textAllCaps="false"
                    app:icon="@drawable/ic_settings"
                    app:strokeColor="@color/islamic_green_primary" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/button_about"
                    style="@style/Widget.Material3.Button.OutlinedButton"
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:text="@string/about"
                    android:textAllCaps="false"
                    app:icon="@drawable/ic_settings"
                    app:strokeColor="@color/islamic_green_primary" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- App Info -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:cardCornerRadius="16dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:text="@string/app_name"
                    android:textColor="@color/islamic_green_primary"
                    android:textSize="20sp"
                    android:textStyle="bold" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:gravity="center"
                    android:text="Version 1.0.0"
                    android:textColor="@color/text_secondary"
                    android:textSize="14sp" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:gravity="center"
                    android:text="A comprehensive Islamic lifestyle app"
                    android:textColor="@color/text_secondary"
                    android:textSize="14sp" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

    </LinearLayout>

</ScrollView>
