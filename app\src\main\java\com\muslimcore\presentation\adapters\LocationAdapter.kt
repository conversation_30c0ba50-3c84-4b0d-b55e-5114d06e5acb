package com.muslimcore.presentation.adapters

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.muslimcore.data.local.managers.LocationManager
import com.muslimcore.databinding.ItemLocationBinding

class LocationAdapter(
    private val onLocationSelected: (LocationManager.LocationData) -> Unit
) : ListAdapter<LocationManager.LocationData, LocationAdapter.LocationViewHolder>(LocationDiffCallback()) {

    private var selectedLocation: LocationManager.LocationData? = null

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): LocationViewHolder {
        val binding = ItemLocationBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return LocationViewHolder(binding)
    }

    override fun onBindViewHolder(holder: LocationViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    fun setSelectedLocation(location: LocationManager.LocationData?) {
        val oldSelected = selectedLocation
        selectedLocation = location
        
        // Update old selected item
        oldSelected?.let { old ->
            val oldIndex = currentList.indexOfFirst { it.cityName == old.cityName && it.countryName == old.countryName }
            if (oldIndex != -1) notifyItemChanged(oldIndex)
        }
        
        // Update new selected item
        location?.let { new ->
            val newIndex = currentList.indexOfFirst { it.cityName == new.cityName && it.countryName == new.countryName }
            if (newIndex != -1) notifyItemChanged(newIndex)
        }
    }

    inner class LocationViewHolder(
        private val binding: ItemLocationBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(location: LocationManager.LocationData) {
            binding.apply {
                textCityName.text = location.cityName
                textCountryName.text = location.countryName
                
                // Show check icon if selected
                val isSelected = selectedLocation?.let { 
                    it.cityName == location.cityName && it.countryName == location.countryName 
                } ?: false
                
                iconSelected.visibility = if (isSelected) View.VISIBLE else View.GONE
                
                root.setOnClickListener {
                    onLocationSelected(location)
                    setSelectedLocation(location)
                }
            }
        }
    }

    private class LocationDiffCallback : DiffUtil.ItemCallback<LocationManager.LocationData>() {
        override fun areItemsTheSame(
            oldItem: LocationManager.LocationData,
            newItem: LocationManager.LocationData
        ): Boolean {
            return oldItem.cityName == newItem.cityName && oldItem.countryName == newItem.countryName
        }

        override fun areContentsTheSame(
            oldItem: LocationManager.LocationData,
            newItem: LocationManager.LocationData
        ): Boolean {
            return oldItem == newItem
        }
    }
}
