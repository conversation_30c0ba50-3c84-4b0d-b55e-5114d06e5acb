<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/dialog_background"
    android:orientation="vertical"
    android:padding="24dp">

    <!-- Title -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Select Location"
        android:textColor="#FFFFFF"
        android:textSize="20sp"
        android:textStyle="bold"
        android:gravity="center"
        android:layout_marginBottom="24dp" />

    <!-- Current Location Section -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="16dp">

        <ImageView
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/ic_location"
            app:tint="#4CAF50"
            android:layout_marginEnd="12dp" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Current Location"
                android:textColor="#FFFFFF"
                android:textSize="14sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/text_current_location"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Detecting..."
                android:textColor="#B0B0B0"
                android:textSize="12sp" />

        </LinearLayout>

        <Button
            android:id="@+id/button_use_current"
            android:layout_width="wrap_content"
            android:layout_height="32dp"
            android:text="Use"
            android:textColor="#FFFFFF"
            android:textSize="12sp"
            android:background="@drawable/button_rounded_small"
            android:minWidth="60dp" />

    </LinearLayout>

    <!-- Search Section -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Search City"
        android:textColor="#FFFFFF"
        android:textSize="16sp"
        android:textStyle="bold"
        android:layout_marginBottom="8dp" />

    <EditText
        android:id="@+id/edit_search"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:background="@drawable/edit_text_background"
        android:hint="Enter city name..."
        android:textColorHint="#80FFFFFF"
        android:textColor="#FFFFFF"
        android:textSize="14sp"
        android:padding="12dp"
        android:layout_marginBottom="16dp"
        android:drawableStart="@drawable/ic_search"
        android:drawablePadding="12dp"
        app:drawableTint="#80FFFFFF" />

    <!-- Popular Cities Section -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Popular Islamic Cities"
        android:textColor="#FFFFFF"
        android:textSize="16sp"
        android:textStyle="bold"
        android:layout_marginBottom="8dp" />

    <!-- Cities RecyclerView -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycler_cities"
        android:layout_width="match_parent"
        android:layout_height="200dp"
        android:layout_marginBottom="24dp" />

    <!-- Action Buttons -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center">

        <Button
            android:id="@+id/button_cancel"
            android:layout_width="100dp"
            android:layout_height="40dp"
            android:layout_marginEnd="16dp"
            android:text="Cancel"
            android:textColor="#FFFFFF"
            android:textSize="14sp"
            android:background="@drawable/cancel_button_selector" />

        <Button
            android:id="@+id/button_done"
            android:layout_width="100dp"
            android:layout_height="40dp"
            android:text="Done"
            android:textColor="#FFFFFF"
            android:textSize="14sp"
            android:background="@drawable/done_button_selector" />

    </LinearLayout>

</LinearLayout>
