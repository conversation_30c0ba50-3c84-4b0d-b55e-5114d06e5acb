package com.muslimcore.presentation.dialogs

import android.app.Dialog
import android.content.Context
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.Gravity
import android.view.WindowManager
import androidx.fragment.app.DialogFragment
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.muslimcore.data.local.managers.LocationManager
import com.muslimcore.databinding.DialogLocationPickerBinding
import com.muslimcore.presentation.adapters.LocationAdapter
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

class LocationPickerDialog(
    private val locationManager: LocationManager,
    private val onLocationSelected: (LocationManager.LocationData) -> Unit
) : DialogFragment() {

    private var _binding: DialogLocationPickerBinding? = null
    private val binding get() = _binding!!
    
    private lateinit var locationAdapter: LocationAdapter
    private var currentLocation: LocationManager.LocationData? = null
    private var selectedLocation: LocationManager.LocationData? = null
    private var searchJob: Job? = null

    companion object {
        fun newInstance(
            locationManager: LocationManager,
            onLocationSelected: (LocationManager.LocationData) -> Unit
        ): LocationPickerDialog {
            return LocationPickerDialog(locationManager, onLocationSelected)
        }
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        _binding = DialogLocationPickerBinding.inflate(layoutInflater)
        
        setupRecyclerView()
        setupClickListeners()
        setupSearch()
        loadCurrentLocation()
        loadPopularCities()
        
        val dialog = Dialog(requireContext(), android.R.style.Theme_Translucent_NoTitleBar)
        dialog.setContentView(binding.root)
        
        // Position dialog
        val window = dialog.window
        window?.let {
            val layoutParams = it.attributes
            layoutParams.gravity = Gravity.CENTER
            layoutParams.width = WindowManager.LayoutParams.MATCH_PARENT
            layoutParams.height = WindowManager.LayoutParams.WRAP_CONTENT
            it.attributes = layoutParams
        }
        
        return dialog
    }

    private fun setupRecyclerView() {
        locationAdapter = LocationAdapter { location ->
            selectedLocation = location
        }
        
        binding.recyclerCities.apply {
            layoutManager = LinearLayoutManager(requireContext())
            adapter = locationAdapter
        }
    }

    private fun setupClickListeners() {
        binding.buttonUseCurrentLocation.setOnClickListener {
            currentLocation?.let { location ->
                selectedLocation = location
                locationAdapter.setSelectedLocation(location)
            }
        }
        
        binding.buttonCancel.setOnClickListener {
            dismiss()
        }
        
        binding.buttonDone.setOnClickListener {
            selectedLocation?.let { location ->
                onLocationSelected(location)
                dismiss()
            }
        }
    }

    private fun setupSearch() {
        binding.editSearch.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
            
            override fun afterTextChanged(s: Editable?) {
                val query = s?.toString()?.trim()
                if (query.isNullOrEmpty()) {
                    loadPopularCities()
                } else {
                    searchLocations(query)
                }
            }
        })
    }

    private fun loadCurrentLocation() {
        lifecycleScope.launch {
            try {
                binding.textCurrentLocation.text = "Detecting..."
                currentLocation = locationManager.getCurrentLocation()
                
                currentLocation?.let { location ->
                    binding.textCurrentLocation.text = "${location.cityName}, ${location.countryName}"
                    binding.buttonUseCurrentLocation.isEnabled = true
                } ?: run {
                    binding.textCurrentLocation.text = "Unable to detect location"
                    binding.buttonUseCurrentLocation.isEnabled = false
                }
            } catch (e: Exception) {
                binding.textCurrentLocation.text = "Location error"
                binding.buttonUseCurrentLocation.isEnabled = false
            }
        }
    }

    private fun loadPopularCities() {
        val popularCities = locationManager.getPopularIslamicCities()
        locationAdapter.submitList(popularCities)
    }

    private fun searchLocations(query: String) {
        // Cancel previous search
        searchJob?.cancel()
        
        searchJob = lifecycleScope.launch {
            try {
                // Add delay to avoid too many API calls
                delay(500)
                
                val searchResults = locationManager.searchLocations(query)
                if (searchResults.isNotEmpty()) {
                    locationAdapter.submitList(searchResults)
                } else {
                    // Show popular cities if no results
                    loadPopularCities()
                }
            } catch (e: Exception) {
                // Show popular cities on error
                loadPopularCities()
            }
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        searchJob?.cancel()
        _binding = null
    }
}
