<?xml version="1.0" encoding="utf-8"?>
<!-- EXACTLY LIKE QURAN.COM - INSPECTED FROM THEIR WEBSITE -->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"

    android:orientation="vertical"
    android:paddingHorizontal="16dp"
    android:paddingVertical="16dp">

    <!-- Bismillah EXACTLY like Quran.com -->
    <ImageView
        android:id="@+id/image_bismillah"
        android:layout_width="match_parent"
        android:layout_height="60dp"
        android:layout_marginBottom="24dp"
        android:adjustViewBounds="true"
        android:scaleType="centerInside"
        android:src="@drawable/bismillah"
        android:visibility="gone"
        tools:visibility="visible" />

    <!-- Verse Container EXACTLY like Quran.com -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="end"
        android:layoutDirection="rtl">

        <!-- Arabic Text with Ayah Number INLINE - EXACTLY like Quran.com -->
        <TextView
            android:id="@+id/text_ayah_arabic_with_number"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textDirection="rtl"
            android:textAlignment="textStart"
            android:lineSpacingMultiplier="1.2"
            android:textColor="#FFFFFF"
            android:textSize="28sp"
            android:paddingHorizontal="1dp"
            android:paddingVertical="12dp"
            android:clipToPadding="false"
            tools:text="الٓمٓ ١" />

    </LinearLayout>

    <!-- Translation EXACTLY like Quran.com -->
    <TextView
        android:id="@+id/text_ayah_translation"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:fontFamily="sans-serif"
        android:gravity="start"
        android:lineSpacingMultiplier="1.4"
        android:textColor="#B0B0B0"
        android:textSize="16sp"
        android:paddingHorizontal="8dp"
        android:visibility="gone"
        tools:text="Alif-Lãm-Mĩm."
        tools:visibility="visible" />

</LinearLayout>
