<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/quran_background">

    <!-- App Bar Layout for auto-hide header -->
    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/app_bar_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@android:color/transparent"
        app:elevation="0dp">

        <!-- Header -->
        <LinearLayout
            android:id="@+id/header_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@android:color/transparent"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:padding="16dp"
            app:layout_scrollFlags="scroll|enterAlways">

        <ImageButton
            android:id="@+id/button_back"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_arrow_left"
            app:tint="@android:color/white" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:orientation="vertical">

            <TextView
                android:id="@+id/text_surah_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/quran_text_arabic"
                android:textSize="18sp"
                android:textStyle="bold"
                tools:text="2. Al-Baqara (The Cow)" />

            <TextView
                android:id="@+id/text_verse_count"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:textColor="@color/quran_text_translation"
                android:textSize="14sp"
                tools:text="2 / 286" />

        </LinearLayout>

        <!-- Settings Button Only -->
        <ImageButton
            android:id="@+id/button_settings"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_settings_outline"
            app:tint="#FFFFFF" />

        </LinearLayout>

    </com.google.android.material.appbar.AppBarLayout>

    <!-- Content with scroll behavior -->
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <!-- Ayah Scroll Indicator -->
        <LinearLayout
            android:id="@+id/ayah_scroll_indicator"
            android:layout_width="40dp"
            android:layout_height="match_parent"
            android:layout_gravity="start"
            android:layout_marginTop="40dp"
            android:layout_marginBottom="40dp"
            android:background="@android:color/transparent"
            android:orientation="vertical"
            android:padding="8dp"
            android:visibility="gone">

            <!-- Scroll Track -->
            <View
                android:id="@+id/scroll_track"
                android:layout_width="4dp"
                android:layout_height="match_parent"
                android:layout_gravity="center_horizontal"
                android:background="@drawable/scroll_track_border" />

            <!-- Scroll Thumb -->
            <View
                android:id="@+id/scroll_thumb"
                android:layout_width="8dp"
                android:layout_height="20dp"
                android:layout_gravity="center_horizontal"
                android:background="#FFFFFF"
                android:layout_marginTop="0dp" />

        </LinearLayout>

        <!-- Ayahs RecyclerView with bismillah as first item -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycler_view_ayahs"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginStart="40dp"
            android:clipToPadding="false"
            android:paddingHorizontal="0dp"
            android:paddingVertical="0dp"
            tools:itemCount="7"
            tools:listitem="@layout/item_ayah" />

        <!-- Loading Progress -->
        <ProgressBar
            android:id="@+id/progress_bar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:visibility="gone" />

        <!-- Floating Ayah Number Indicator -->
        <TextView
            android:id="@+id/floating_ayah_indicator"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="start|top"
            android:layout_marginStart="50dp"
            android:background="#80000000"
            android:padding="8dp"
            android:text="1"
            android:textColor="#FFFFFF"
            android:textSize="12sp"
            android:visibility="gone" />

    </FrameLayout>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
