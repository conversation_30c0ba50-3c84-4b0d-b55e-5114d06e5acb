-- Merging decision tree log ---
provider#androidx.startup.InitializationProvider
INJECTED from C:\Muslim Core\app\src\main\AndroidManifest.xml:95:9-104:20
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\39a8a2aca2bad19af9fb513e99abd016\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\39a8a2aca2bad19af9fb513e99abd016\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:29:9-37:20
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:29:9-37:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\683da4f7ef5696e7dcd83bd164cc9897\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\683da4f7ef5696e7dcd83bd164cc9897\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ce2fbb511b1dd1207b7023c44f05bb81\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ce2fbb511b1dd1207b7023c44f05bb81\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f059acc78cb16502a6170c70ab7b48a3\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f059acc78cb16502a6170c70ab7b48a3\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from C:\Muslim Core\app\src\main\AndroidManifest.xml:99:13-31
	android:authorities
		INJECTED from C:\Muslim Core\app\src\main\AndroidManifest.xml
		ADDED from C:\Muslim Core\app\src\main\AndroidManifest.xml:97:13-68
	android:exported
		ADDED from C:\Muslim Core\app\src\main\AndroidManifest.xml:98:13-37
	android:name
		ADDED from C:\Muslim Core\app\src\main\AndroidManifest.xml:96:13-67
manifest
ADDED from C:\Muslim Core\app\src\main\AndroidManifest.xml:2:1-108:12
INJECTED from C:\Muslim Core\app\src\main\AndroidManifest.xml:2:1-108:12
INJECTED from C:\Muslim Core\app\src\main\AndroidManifest.xml:2:1-108:12
INJECTED from C:\Muslim Core\app\src\main\AndroidManifest.xml:2:1-108:12
MERGED from [androidx.databinding:databinding-adapters:8.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\ce93ce5bd45704ff32cf7f9b33dbc2e2\transformed\databinding-adapters-8.1.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.databinding:databinding-ktx:8.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\d3d8e8d40fcc52a7ca567090c9bb9584\transformed\databinding-ktx-8.1.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.databinding:databinding-runtime:8.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\91f5ac35e2aac1e5ec49fa8326c1819a\transformed\databinding-runtime-8.1.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.databinding:viewbinding:8.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\21bd60ff35e03baea11470532ebdf25c\transformed\viewbinding-8.1.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.preference:preference-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\883ed09f89aea110c50d59ef0b3bce1d\transformed\preference-ktx-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.navigation:navigation-common:2.7.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\eea476f82e6112e83a24b6c42880b608\transformed\navigation-common-2.7.4\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-ktx:2.7.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\4643f83ad22a1c94dcdf8fdea70cbfd7\transformed\navigation-common-ktx-2.7.4\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-runtime:2.7.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\69000e4e45894c9884b32a91ee0b736c\transformed\navigation-runtime-2.7.4\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-ui:2.7.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\cacdfc042dfae4e43c008dc67295820e\transformed\navigation-ui-2.7.4\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\f96b327603e054eec2a5940deb11cfd0\transformed\navigation-runtime-ktx-2.7.4\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-ui-ktx:2.7.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\2c9ea3e49a19606d7ec080362802a64a\transformed\navigation-ui-ktx-2.7.4\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-fragment-ktx:2.7.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\79f7d1071fac12787ccb18c3f9960464\transformed\navigation-fragment-ktx-2.7.4\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-fragment:2.7.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\f2e0173b205ab009bcb83bc37b0023a7\transformed\navigation-fragment-2.7.4\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.material:material:1.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\23d7c820469d7d72a3f613bbedf53593\transformed\material-1.10.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\fd9e756f56bf090cd291cb3d53d819c4\transformed\constraintlayout-2.1.4\AndroidManifest.xml:2:1-11:12
MERGED from [com.airbnb.android:lottie:6.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc7a596ca09ec1789d8b31b6cf9b156f\transformed\lottie-6.1.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\527f5e9a80e364e458d082bf65a46dd8\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\2025416948d6099286a1c5deac9fc59b\transformed\preference-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e6699e020abbc856e9d4962d528675a8\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.dagger:hilt-android:2.48] C:\Users\<USER>\.gradle\caches\8.12\transforms\3206c75f50878f9df80e75eb8148db71\transformed\hilt-android-2.48\AndroidManifest.xml:16:1-19:12
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ce2d8fddf65a1d1c885d98f45b4a48f\transformed\play-services-maps-18.2.0\AndroidManifest.xml:17:1-44:12
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f603684ac85a3923c1b73c512f406c56\transformed\glide-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\332273db20fb8075f00b021e7f25ead7\transformed\play-services-location-21.0.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.firebase:firebase-firestore-ktx:24.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\6cb3da3a0e28fbb3430bbd354b98bd68\transformed\firebase-firestore-ktx-24.9.1\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-firestore:24.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\bc763f29c7783602ce54f376238f5573\transformed\firebase-firestore-24.9.1\AndroidManifest.xml:2:1-26:12
MERGED from [com.google.firebase:firebase-messaging-ktx:23.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f02b071850434d6569dac072ad31c2fd\transformed\firebase-messaging-ktx-23.3.1\AndroidManifest.xml:17:1-32:12
MERGED from [com.google.firebase:firebase-messaging:23.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\657d4b970ca786a19c67b9546b583e6a\transformed\firebase-messaging-23.3.1\AndroidManifest.xml:17:1-66:12
MERGED from [com.google.firebase:firebase-auth-ktx:22.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a121f920b99ddcf8e2e1aac0ac05b0f9\transformed\firebase-auth-ktx-22.3.0\AndroidManifest.xml:2:1-17:12
MERGED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fe4f96e904e00d58c3ce6afc4255acc8\transformed\firebase-auth-22.3.0\AndroidManifest.xml:17:1-75:12
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7ec7858ef834c086f5b58a24ad7fd8fe\transformed\firebase-appcheck-interop-17.0.0\AndroidManifest.xml:15:1-25:12
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\4086e7d05b484f7ea43cbb5ea3d43a01\transformed\firebase-database-collection-18.0.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-auth-api-phone:17.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a539898345436c8142f74151471946dd\transformed\play-services-auth-api-phone-17.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bd229cba7731028eaebcf7d452c4e6dc\transformed\play-services-base-18.1.0\AndroidManifest.xml:16:1-24:12
MERGED from [com.google.firebase:firebase-config-ktx:21.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1a29fdd56c9ef7f7a8d2faae48bfc03d\transformed\firebase-config-ktx-21.6.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\462349714f38d054982a20853c15eade\transformed\firebase-analytics-ktx-21.5.0\AndroidManifest.xml:2:1-17:12
MERGED from [com.google.firebase:firebase-config:21.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7d7bd6579bd35503adb2fe395d71ea3e\transformed\firebase-config-21.6.0\AndroidManifest.xml:17:1-38:12
MERGED from [com.google.firebase:firebase-analytics:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\22ba9d1314a24d427d816c3b92a77350\transformed\firebase-analytics-21.5.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c42e3f53f251f72190ffef6945138b19\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:17:1-43:12
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4550ebb1f57cdacda2347951a8b355e7\transformed\firebase-installations-17.2.0\AndroidManifest.xml:2:1-24:12
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\ce87eaf2450b4f0f51a49634d42a1887\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5f697a4a3151cf4487d51aef6b9528c1\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\29a9afe92f618d0e0b754a9bfb76a586\transformed\firebase-common-20.4.2\AndroidManifest.xml:15:1-41:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7cda915d6bf6539208b46b00879d8712\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\39a8a2aca2bad19af9fb513e99abd016\transformed\emoji2-1.2.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\8.12\transforms\e9053bce3f72d4aea6ccbcff5e628126\transformed\viewpager2-1.1.0-beta02\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\dea3e6fcf5697c9423505c2045e63399\transformed\recyclerview-1.3.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fb1d9df47d7b984f4dadfbaace818bfe\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a9ad9f627cca6e719a539b990757f1a8\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\2909155116065c1feba18126652ab8b1\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity:1.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f2278bd78dd5b8de51d11a4c518bddc3\transformed\activity-1.8.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b3220947d7de38838cf340bae783eebe\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2bcf7493bd089a2ab471d9e5de7b9724\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.work:work-runtime-ktx:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\610865b8ffab8bd44e532006577a1196\transformed\work-runtime-ktx-2.8.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:17:1-145:12
MERGED from [androidx.lifecycle:lifecycle-service:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dbcda8e42cd24d91c642ef6749aeeaff\transformed\lifecycle-service-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2e7b31cf85e628a427d204ca3c1e1d04\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4cce030851b64fce14a30f35636cafff\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:17:1-46:12
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2054581ecb8aae4e1f94cbef4ff3eeed\transformed\play-services-measurement-sdk-21.5.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\08cd672f2bf9942df1c4039c23a8420a\transformed\play-services-measurement-impl-21.5.0\AndroidManifest.xml:17:1-32:12
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.12\transforms\6767009578d7e0362def9a7abe632d78\transformed\ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.12\transforms\135d01c71a961225103e48b7dd206008\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6d2285c0a3d448473ee7ff90d5118c5a\transformed\core-ktx-1.12.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7c41441c1adccc084d55e76797449120\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\2829f477d9c6056a40040b143f304a8b\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b6d17d87dae82d892d5ed1b2c35b7a41\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\999abc7c220f9e0685ae9357592681d0\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b7dfa5d4184699e0b717fb7b27565e40\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d78166393493a751715ec2d1827d3b3c\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fca31f41437b434ebf5863b0af1ea5a7\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\03584becdb334b030db50442e94ad5c3\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\775b0f6bc0598e3fa176fb3ab128693d\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\36f2737b82835d7118612a5b23b076a1\transformed\play-services-stats-17.0.2\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b018e785c25bf37ddd4e8ab57d8834e7\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7b69551ac5e8933639210b54b8e1f4b0\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.browser:browser:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\81561af282a29cce9274e37ca4fb03dd\transformed\browser-1.4.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5ce941577190a4b7e40da0e194a20c68\transformed\transition-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e661d63d9c04259b2495b443a4caf917\transformed\window-1.0.0\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\29aa2070c5f46de89120ba6e20f2606d\transformed\core-1.12.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8c3942081edffb4b875bc5f4359e13d8\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\683da4f7ef5696e7dcd83bd164cc9897\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\52cfa56fd2ad7827c32e9e051048beda\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\382da343d6fc045de39887588a3cc4a8\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.recaptcha:recaptcha:18.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\9ceca76805a3be07b1bd347340a44134\transformed\recaptcha-18.1.2\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\47a09af950e9d4633e43ca23a3c9a0bb\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:15:1-19:12
MERGED from [com.google.android.play:integrity:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b0cd884fbf9a6450705aeb223448a38c\transformed\integrity-1.1.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fb4647b322715c176135d86e4e428266\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\516259ec442d5b9ec5e79b98a8050845\transformed\play-services-cloud-messaging-17.0.1\AndroidManifest.xml:2:1-13:12
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\a0eb2e2a12f92faafc8f0b774c5db902\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:2:1-6:12
MERGED from [com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5565bd1e3d8bedbb48c3d9ebd009d2d6\transformed\firebase-abt-21.1.1\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4eb2364817c7d34bb93acc989f7bd3db\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cb231c7e35432be62c560b860c915b21\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:17:1-27:12
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2e44da5bd1abc324905da37f6753f3bb\transformed\play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:17:1-30:12
MERGED from [com.google.android.gms:play-services-measurement-base:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1239f049c4746005cd1af317bc2e4ecd\transformed\play-services-measurement-base-21.5.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\812136ae31eafce15fafbc5dfed6f6ac\transformed\play-services-basement-18.1.0\AndroidManifest.xml:16:1-26:12
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\14523b378ddeb224f3413513055a3ace\transformed\fragment-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\859caf9af04706cbb82a4303b043aa18\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1a669755bc0544f538ac3665dbb69afc\transformed\activity-ktx-1.8.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0ca9a8cd02d7bb1dc6fa2bacc70e914e\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\93023eb18092c846c4a0d8a59ef6d371\transformed\lifecycle-livedata-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ce0a286f5a517a5411a0f92eb45a7234\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.room:room-runtime:2.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\baa7f398d5d0d1385b12ae8bfc214adc\transformed\room-runtime-2.6.0\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.room:room-ktx:2.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\defb090a73c05d9bd9af7eac4735961f\transformed\room-ktx-2.6.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.firebase:firebase-config-interop:16.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\33b14fe4b141a0d93205f4ffe3f88514\transformed\firebase-config-interop-16.0.0\AndroidManifest.xml:15:1-20:12
MERGED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\3ca3bb862412792bd96083ec00cacda6\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:15:1-33:12
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.12\transforms\683f793d9b7aa5372f5619ebe6359687\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:15:1-37:12
MERGED from [com.google.firebase:firebase-encoders-json:18.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a74e6f76ec28a257e238d2e8697e5c8\transformed\firebase-encoders-json-18.0.1\AndroidManifest.xml:15:1-22:12
MERGED from [androidx.core:core-splashscreen:1.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cdebcb5cea68a5cacee791078fa3b341\transformed\core-splashscreen-1.0.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.12\transforms\4d198d6f53404638e45dff19d597f6a7\transformed\transport-runtime-3.1.8\AndroidManifest.xml:15:1-41:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a39ac11765bd508aeac5f649e35b4c9\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ab9ce3d6fabc773074c27a4a16889e6f\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\ebcadbd030c7c25414955d10f0fdf41b\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9855521e152600f5c73ac08925a289ee\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f377f2617dbea8c7b9f73ab7b4573b28\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dbf57c3e0f51ef1f88e5786411429f26\transformed\sqlite-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2b73d759b335a274cdc83dddb7f4dbe3\transformed\gifdecoder-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\067a0fabd78bbf4cca26a52ee7c34f16\transformed\exifinterface-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ce2fbb511b1dd1207b7023c44f05bb81\transformed\profileinstaller-1.3.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f059acc78cb16502a6170c70ab7b48a3\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9dc23eb98257d1a42135c42113fc6bcc\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\13d57602bd55674a929e79ea990e7f02\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.firebase:firebase-components:17.1.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\6e73913549ad530644c8e168ab1e59d6\transformed\firebase-components-17.1.5\AndroidManifest.xml:15:1-20:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\00239ce5fe5e25cd05268188b7bd15c1\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.datatransport:transport-api:3.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\af399a98b4fd4de64551879c9099860e\transformed\transport-api-3.0.0\AndroidManifest.xml:15:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a12e2f63101da8b7e81b22f914720ee3\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\63d7229498fc9f109441ea9f24dad33a\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8cac734f3b70a6524ee8666ddab0ba00\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [io.grpc:grpc-android:1.52.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\6c03df68e93751f100c940675ea19a00\transformed\grpc-android-1.52.1\AndroidManifest.xml:2:1-11:12
MERGED from [com.google.dagger:dagger-lint-aar:2.48] C:\Users\<USER>\.gradle\caches\8.12\transforms\69aa2ec88c4b2e954a0ccb3c5ea074a1\transformed\dagger-lint-aar-2.48\AndroidManifest.xml:16:1-19:12
MERGED from [com.google.firebase:protolite-well-known-types:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dfa0dd3b089da752fa5e8b6008302b33\transformed\protolite-well-known-types-18.0.0\AndroidManifest.xml:2:1-11:12
	package
		INJECTED from C:\Muslim Core\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Muslim Core\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Muslim Core\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Muslim Core\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Muslim Core\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from C:\Muslim Core\app\src\main\AndroidManifest.xml:6:5-67
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ce2d8fddf65a1d1c885d98f45b4a48f\transformed\play-services-maps-18.2.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ce2d8fddf65a1d1c885d98f45b4a48f\transformed\play-services-maps-18.2.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.firebase:firebase-firestore:24.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\bc763f29c7783602ce54f376238f5573\transformed\firebase-firestore-24.9.1\AndroidManifest.xml:11:5-67
MERGED from [com.google.firebase:firebase-firestore:24.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\bc763f29c7783602ce54f376238f5573\transformed\firebase-firestore-24.9.1\AndroidManifest.xml:11:5-67
MERGED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fe4f96e904e00d58c3ce6afc4255acc8\transformed\firebase-auth-22.3.0\AndroidManifest.xml:25:5-67
MERGED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fe4f96e904e00d58c3ce6afc4255acc8\transformed\firebase-auth-22.3.0\AndroidManifest.xml:25:5-67
MERGED from [com.google.firebase:firebase-config:21.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7d7bd6579bd35503adb2fe395d71ea3e\transformed\firebase-config-21.6.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.firebase:firebase-config:21.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7d7bd6579bd35503adb2fe395d71ea3e\transformed\firebase-config-21.6.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c42e3f53f251f72190ffef6945138b19\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:22:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c42e3f53f251f72190ffef6945138b19\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:22:5-67
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4550ebb1f57cdacda2347951a8b355e7\transformed\firebase-installations-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4550ebb1f57cdacda2347951a8b355e7\transformed\firebase-installations-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4cce030851b64fce14a30f35636cafff\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4cce030851b64fce14a30f35636cafff\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\08cd672f2bf9942df1c4039c23a8420a\transformed\play-services-measurement-impl-21.5.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\08cd672f2bf9942df1c4039c23a8420a\transformed\play-services-measurement-impl-21.5.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.recaptcha:recaptcha:18.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\9ceca76805a3be07b1bd347340a44134\transformed\recaptcha-18.1.2\AndroidManifest.xml:7:5-67
MERGED from [com.google.android.recaptcha:recaptcha:18.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\9ceca76805a3be07b1bd347340a44134\transformed\recaptcha-18.1.2\AndroidManifest.xml:7:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\516259ec442d5b9ec5e79b98a8050845\transformed\play-services-cloud-messaging-17.0.1\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\516259ec442d5b9ec5e79b98a8050845\transformed\play-services-cloud-messaging-17.0.1\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2e44da5bd1abc324905da37f6753f3bb\transformed\play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2e44da5bd1abc324905da37f6753f3bb\transformed\play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.12\transforms\683f793d9b7aa5372f5619ebe6359687\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:25:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.12\transforms\683f793d9b7aa5372f5619ebe6359687\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:25:5-67
	android:name
		ADDED from C:\Muslim Core\app\src\main\AndroidManifest.xml:6:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from C:\Muslim Core\app\src\main\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ce2d8fddf65a1d1c885d98f45b4a48f\transformed\play-services-maps-18.2.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ce2d8fddf65a1d1c885d98f45b4a48f\transformed\play-services-maps-18.2.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.firebase:firebase-firestore:24.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\bc763f29c7783602ce54f376238f5573\transformed\firebase-firestore-24.9.1\AndroidManifest.xml:10:5-79
MERGED from [com.google.firebase:firebase-firestore:24.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\bc763f29c7783602ce54f376238f5573\transformed\firebase-firestore-24.9.1\AndroidManifest.xml:10:5-79
MERGED from [com.google.firebase:firebase-messaging:23.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\657d4b970ca786a19c67b9546b583e6a\transformed\firebase-messaging-23.3.1\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-messaging:23.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\657d4b970ca786a19c67b9546b583e6a\transformed\firebase-messaging-23.3.1\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fe4f96e904e00d58c3ce6afc4255acc8\transformed\firebase-auth-22.3.0\AndroidManifest.xml:26:5-79
MERGED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fe4f96e904e00d58c3ce6afc4255acc8\transformed\firebase-auth-22.3.0\AndroidManifest.xml:26:5-79
MERGED from [com.google.firebase:firebase-config:21.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7d7bd6579bd35503adb2fe395d71ea3e\transformed\firebase-config-21.6.0\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-config:21.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7d7bd6579bd35503adb2fe395d71ea3e\transformed\firebase-config-21.6.0\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c42e3f53f251f72190ffef6945138b19\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c42e3f53f251f72190ffef6945138b19\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4550ebb1f57cdacda2347951a8b355e7\transformed\firebase-installations-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4550ebb1f57cdacda2347951a8b355e7\transformed\firebase-installations-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:24:5-79
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4cce030851b64fce14a30f35636cafff\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4cce030851b64fce14a30f35636cafff\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\08cd672f2bf9942df1c4039c23a8420a\transformed\play-services-measurement-impl-21.5.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\08cd672f2bf9942df1c4039c23a8420a\transformed\play-services-measurement-impl-21.5.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.recaptcha:recaptcha:18.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\9ceca76805a3be07b1bd347340a44134\transformed\recaptcha-18.1.2\AndroidManifest.xml:8:5-79
MERGED from [com.google.android.recaptcha:recaptcha:18.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\9ceca76805a3be07b1bd347340a44134\transformed\recaptcha-18.1.2\AndroidManifest.xml:8:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\516259ec442d5b9ec5e79b98a8050845\transformed\play-services-cloud-messaging-17.0.1\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\516259ec442d5b9ec5e79b98a8050845\transformed\play-services-cloud-messaging-17.0.1\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2e44da5bd1abc324905da37f6753f3bb\transformed\play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2e44da5bd1abc324905da37f6753f3bb\transformed\play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.12\transforms\683f793d9b7aa5372f5619ebe6359687\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.12\transforms\683f793d9b7aa5372f5619ebe6359687\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.12\transforms\4d198d6f53404638e45dff19d597f6a7\transformed\transport-runtime-3.1.8\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.12\transforms\4d198d6f53404638e45dff19d597f6a7\transformed\transport-runtime-3.1.8\AndroidManifest.xml:22:5-79
MERGED from [io.grpc:grpc-android:1.52.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\6c03df68e93751f100c940675ea19a00\transformed\grpc-android-1.52.1\AndroidManifest.xml:9:5-79
MERGED from [io.grpc:grpc-android:1.52.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\6c03df68e93751f100c940675ea19a00\transformed\grpc-android-1.52.1\AndroidManifest.xml:9:5-79
	android:name
		ADDED from C:\Muslim Core\app\src\main\AndroidManifest.xml:7:22-76
uses-permission#android.permission.ACCESS_FINE_LOCATION
ADDED from C:\Muslim Core\app\src\main\AndroidManifest.xml:8:5-79
	android:name
		ADDED from C:\Muslim Core\app\src\main\AndroidManifest.xml:8:22-76
uses-permission#android.permission.ACCESS_COARSE_LOCATION
ADDED from C:\Muslim Core\app\src\main\AndroidManifest.xml:9:5-81
	android:name
		ADDED from C:\Muslim Core\app\src\main\AndroidManifest.xml:9:22-78
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from C:\Muslim Core\app\src\main\AndroidManifest.xml:10:5-77
MERGED from [com.google.firebase:firebase-messaging:23.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\657d4b970ca786a19c67b9546b583e6a\transformed\firebase-messaging-23.3.1\AndroidManifest.xml:23:5-77
MERGED from [com.google.firebase:firebase-messaging:23.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\657d4b970ca786a19c67b9546b583e6a\transformed\firebase-messaging-23.3.1\AndroidManifest.xml:23:5-77
	android:name
		ADDED from C:\Muslim Core\app\src\main\AndroidManifest.xml:10:22-74
uses-permission#android.permission.WAKE_LOCK
ADDED from C:\Muslim Core\app\src\main\AndroidManifest.xml:11:5-68
MERGED from [com.google.firebase:firebase-messaging:23.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\657d4b970ca786a19c67b9546b583e6a\transformed\firebase-messaging-23.3.1\AndroidManifest.xml:24:5-68
MERGED from [com.google.firebase:firebase-messaging:23.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\657d4b970ca786a19c67b9546b583e6a\transformed\firebase-messaging-23.3.1\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c42e3f53f251f72190ffef6945138b19\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c42e3f53f251f72190ffef6945138b19\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:24:5-68
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:23:5-68
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:23:5-68
MERGED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4cce030851b64fce14a30f35636cafff\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4cce030851b64fce14a30f35636cafff\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\08cd672f2bf9942df1c4039c23a8420a\transformed\play-services-measurement-impl-21.5.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\08cd672f2bf9942df1c4039c23a8420a\transformed\play-services-measurement-impl-21.5.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\516259ec442d5b9ec5e79b98a8050845\transformed\play-services-cloud-messaging-17.0.1\AndroidManifest.xml:9:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\516259ec442d5b9ec5e79b98a8050845\transformed\play-services-cloud-messaging-17.0.1\AndroidManifest.xml:9:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2e44da5bd1abc324905da37f6753f3bb\transformed\play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2e44da5bd1abc324905da37f6753f3bb\transformed\play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:25:5-68
	android:name
		ADDED from C:\Muslim Core\app\src\main\AndroidManifest.xml:11:22-65
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from C:\Muslim Core\app\src\main\AndroidManifest.xml:12:5-77
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:26:5-77
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:26:5-77
	android:name
		ADDED from C:\Muslim Core\app\src\main\AndroidManifest.xml:12:22-74
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from C:\Muslim Core\app\src\main\AndroidManifest.xml:13:5-81
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:25:5-81
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:25:5-81
	android:name
		ADDED from C:\Muslim Core\app\src\main\AndroidManifest.xml:13:22-78
uses-permission#android.permission.SYSTEM_ALERT_WINDOW
ADDED from C:\Muslim Core\app\src\main\AndroidManifest.xml:14:5-78
	android:name
		ADDED from C:\Muslim Core\app\src\main\AndroidManifest.xml:14:22-75
uses-permission#android.permission.VIBRATE
ADDED from C:\Muslim Core\app\src\main\AndroidManifest.xml:17:5-66
	android:name
		ADDED from C:\Muslim Core\app\src\main\AndroidManifest.xml:17:22-63
application
ADDED from C:\Muslim Core\app\src\main\AndroidManifest.xml:19:5-106:19
INJECTED from C:\Muslim Core\app\src\main\AndroidManifest.xml:19:5-106:19
MERGED from [com.google.android.material:material:1.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\23d7c820469d7d72a3f613bbedf53593\transformed\material-1.10.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\23d7c820469d7d72a3f613bbedf53593\transformed\material-1.10.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\fd9e756f56bf090cd291cb3d53d819c4\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\fd9e756f56bf090cd291cb3d53d819c4\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [com.airbnb.android:lottie:6.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc7a596ca09ec1789d8b31b6cf9b156f\transformed\lottie-6.1.0\AndroidManifest.xml:7:5-20
MERGED from [com.airbnb.android:lottie:6.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc7a596ca09ec1789d8b31b6cf9b156f\transformed\lottie-6.1.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ce2d8fddf65a1d1c885d98f45b4a48f\transformed\play-services-maps-18.2.0\AndroidManifest.xml:36:5-42:19
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ce2d8fddf65a1d1c885d98f45b4a48f\transformed\play-services-maps-18.2.0\AndroidManifest.xml:36:5-42:19
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\332273db20fb8075f00b021e7f25ead7\transformed\play-services-location-21.0.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\332273db20fb8075f00b021e7f25ead7\transformed\play-services-location-21.0.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-firestore-ktx:24.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\6cb3da3a0e28fbb3430bbd354b98bd68\transformed\firebase-firestore-ktx-24.9.1\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-firestore-ktx:24.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\6cb3da3a0e28fbb3430bbd354b98bd68\transformed\firebase-firestore-ktx-24.9.1\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-firestore:24.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\bc763f29c7783602ce54f376238f5573\transformed\firebase-firestore-24.9.1\AndroidManifest.xml:13:5-24:19
MERGED from [com.google.firebase:firebase-firestore:24.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\bc763f29c7783602ce54f376238f5573\transformed\firebase-firestore-24.9.1\AndroidManifest.xml:13:5-24:19
MERGED from [com.google.firebase:firebase-messaging-ktx:23.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f02b071850434d6569dac072ad31c2fd\transformed\firebase-messaging-ktx-23.3.1\AndroidManifest.xml:22:5-30:19
MERGED from [com.google.firebase:firebase-messaging-ktx:23.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f02b071850434d6569dac072ad31c2fd\transformed\firebase-messaging-ktx-23.3.1\AndroidManifest.xml:22:5-30:19
MERGED from [com.google.firebase:firebase-messaging:23.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\657d4b970ca786a19c67b9546b583e6a\transformed\firebase-messaging-23.3.1\AndroidManifest.xml:28:5-64:19
MERGED from [com.google.firebase:firebase-messaging:23.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\657d4b970ca786a19c67b9546b583e6a\transformed\firebase-messaging-23.3.1\AndroidManifest.xml:28:5-64:19
MERGED from [com.google.firebase:firebase-auth-ktx:22.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a121f920b99ddcf8e2e1aac0ac05b0f9\transformed\firebase-auth-ktx-22.3.0\AndroidManifest.xml:7:5-15:19
MERGED from [com.google.firebase:firebase-auth-ktx:22.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a121f920b99ddcf8e2e1aac0ac05b0f9\transformed\firebase-auth-ktx-22.3.0\AndroidManifest.xml:7:5-15:19
MERGED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fe4f96e904e00d58c3ce6afc4255acc8\transformed\firebase-auth-22.3.0\AndroidManifest.xml:28:5-73:19
MERGED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fe4f96e904e00d58c3ce6afc4255acc8\transformed\firebase-auth-22.3.0\AndroidManifest.xml:28:5-73:19
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7ec7858ef834c086f5b58a24ad7fd8fe\transformed\firebase-appcheck-interop-17.0.0\AndroidManifest.xml:23:5-20
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7ec7858ef834c086f5b58a24ad7fd8fe\transformed\firebase-appcheck-interop-17.0.0\AndroidManifest.xml:23:5-20
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bd229cba7731028eaebcf7d452c4e6dc\transformed\play-services-base-18.1.0\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bd229cba7731028eaebcf7d452c4e6dc\transformed\play-services-base-18.1.0\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.firebase:firebase-config-ktx:21.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1a29fdd56c9ef7f7a8d2faae48bfc03d\transformed\firebase-config-ktx-21.6.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-config-ktx:21.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1a29fdd56c9ef7f7a8d2faae48bfc03d\transformed\firebase-config-ktx-21.6.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\462349714f38d054982a20853c15eade\transformed\firebase-analytics-ktx-21.5.0\AndroidManifest.xml:7:5-15:19
MERGED from [com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\462349714f38d054982a20853c15eade\transformed\firebase-analytics-ktx-21.5.0\AndroidManifest.xml:7:5-15:19
MERGED from [com.google.firebase:firebase-config:21.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7d7bd6579bd35503adb2fe395d71ea3e\transformed\firebase-config-21.6.0\AndroidManifest.xml:25:5-36:19
MERGED from [com.google.firebase:firebase-config:21.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7d7bd6579bd35503adb2fe395d71ea3e\transformed\firebase-config-21.6.0\AndroidManifest.xml:25:5-36:19
MERGED from [com.google.firebase:firebase-analytics:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\22ba9d1314a24d427d816c3b92a77350\transformed\firebase-analytics-21.5.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-analytics:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\22ba9d1314a24d427d816c3b92a77350\transformed\firebase-analytics-21.5.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c42e3f53f251f72190ffef6945138b19\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:29:5-41:19
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c42e3f53f251f72190ffef6945138b19\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:29:5-41:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4550ebb1f57cdacda2347951a8b355e7\transformed\firebase-installations-17.2.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4550ebb1f57cdacda2347951a8b355e7\transformed\firebase-installations-17.2.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\ce87eaf2450b4f0f51a49634d42a1887\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\ce87eaf2450b4f0f51a49634d42a1887\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5f697a4a3151cf4487d51aef6b9528c1\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5f697a4a3151cf4487d51aef6b9528c1\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\29a9afe92f618d0e0b754a9bfb76a586\transformed\firebase-common-20.4.2\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\29a9afe92f618d0e0b754a9bfb76a586\transformed\firebase-common-20.4.2\AndroidManifest.xml:22:5-39:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\39a8a2aca2bad19af9fb513e99abd016\transformed\emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\39a8a2aca2bad19af9fb513e99abd016\transformed\emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:28:5-143:19
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:28:5-143:19
MERGED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4cce030851b64fce14a30f35636cafff\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4cce030851b64fce14a30f35636cafff\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2054581ecb8aae4e1f94cbef4ff3eeed\transformed\play-services-measurement-sdk-21.5.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2054581ecb8aae4e1f94cbef4ff3eeed\transformed\play-services-measurement-sdk-21.5.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\08cd672f2bf9942df1c4039c23a8420a\transformed\play-services-measurement-impl-21.5.0\AndroidManifest.xml:29:5-30:19
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\08cd672f2bf9942df1c4039c23a8420a\transformed\play-services-measurement-impl-21.5.0\AndroidManifest.xml:29:5-30:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.12\transforms\135d01c71a961225103e48b7dd206008\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.12\transforms\135d01c71a961225103e48b7dd206008\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:22:5-26:19
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\36f2737b82835d7118612a5b23b076a1\transformed\play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\36f2737b82835d7118612a5b23b076a1\transformed\play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e661d63d9c04259b2495b443a4caf917\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e661d63d9c04259b2495b443a4caf917\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\29aa2070c5f46de89120ba6e20f2606d\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\29aa2070c5f46de89120ba6e20f2606d\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\683da4f7ef5696e7dcd83bd164cc9897\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\683da4f7ef5696e7dcd83bd164cc9897\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [com.google.android.play:integrity:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b0cd884fbf9a6450705aeb223448a38c\transformed\integrity-1.1.0\AndroidManifest.xml:5:5-6:19
MERGED from [com.google.android.play:integrity:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b0cd884fbf9a6450705aeb223448a38c\transformed\integrity-1.1.0\AndroidManifest.xml:5:5-6:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fb4647b322715c176135d86e4e428266\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fb4647b322715c176135d86e4e428266\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\a0eb2e2a12f92faafc8f0b774c5db902\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\a0eb2e2a12f92faafc8f0b774c5db902\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:5:5-20
MERGED from [com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5565bd1e3d8bedbb48c3d9ebd009d2d6\transformed\firebase-abt-21.1.1\AndroidManifest.xml:10:5-16:19
MERGED from [com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5565bd1e3d8bedbb48c3d9ebd009d2d6\transformed\firebase-abt-21.1.1\AndroidManifest.xml:10:5-16:19
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4eb2364817c7d34bb93acc989f7bd3db\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4eb2364817c7d34bb93acc989f7bd3db\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cb231c7e35432be62c560b860c915b21\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cb231c7e35432be62c560b860c915b21\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1239f049c4746005cd1af317bc2e4ecd\transformed\play-services-measurement-base-21.5.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1239f049c4746005cd1af317bc2e4ecd\transformed\play-services-measurement-base-21.5.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\812136ae31eafce15fafbc5dfed6f6ac\transformed\play-services-basement-18.1.0\AndroidManifest.xml:20:5-24:19
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\812136ae31eafce15fafbc5dfed6f6ac\transformed\play-services-basement-18.1.0\AndroidManifest.xml:20:5-24:19
MERGED from [androidx.room:room-runtime:2.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\baa7f398d5d0d1385b12ae8bfc214adc\transformed\room-runtime-2.6.0\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime:2.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\baa7f398d5d0d1385b12ae8bfc214adc\transformed\room-runtime-2.6.0\AndroidManifest.xml:23:5-29:19
MERGED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\3ca3bb862412792bd96083ec00cacda6\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:23:5-31:19
MERGED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\3ca3bb862412792bd96083ec00cacda6\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:23:5-31:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.12\transforms\683f793d9b7aa5372f5619ebe6359687\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.12\transforms\683f793d9b7aa5372f5619ebe6359687\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.12\transforms\4d198d6f53404638e45dff19d597f6a7\transformed\transport-runtime-3.1.8\AndroidManifest.xml:25:5-39:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.12\transforms\4d198d6f53404638e45dff19d597f6a7\transformed\transport-runtime-3.1.8\AndroidManifest.xml:25:5-39:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\ebcadbd030c7c25414955d10f0fdf41b\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\ebcadbd030c7c25414955d10f0fdf41b\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ce2fbb511b1dd1207b7023c44f05bb81\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ce2fbb511b1dd1207b7023c44f05bb81\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f059acc78cb16502a6170c70ab7b48a3\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f059acc78cb16502a6170c70ab7b48a3\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
	android:extractNativeLibs
		INJECTED from C:\Muslim Core\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\29aa2070c5f46de89120ba6e20f2606d\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Muslim Core\app\src\main\AndroidManifest.xml:27:9-35
	android:label
		ADDED from C:\Muslim Core\app\src\main\AndroidManifest.xml:25:9-41
	android:fullBackupContent
		ADDED from C:\Muslim Core\app\src\main\AndroidManifest.xml:23:9-54
	android:roundIcon
		ADDED from C:\Muslim Core\app\src\main\AndroidManifest.xml:26:9-54
	tools:targetApi
		ADDED from C:\Muslim Core\app\src\main\AndroidManifest.xml:29:9-29
	android:icon
		ADDED from C:\Muslim Core\app\src\main\AndroidManifest.xml:24:9-43
	android:allowBackup
		ADDED from C:\Muslim Core\app\src\main\AndroidManifest.xml:21:9-35
	android:theme
		ADDED from C:\Muslim Core\app\src\main\AndroidManifest.xml:28:9-48
	android:dataExtractionRules
		ADDED from C:\Muslim Core\app\src\main\AndroidManifest.xml:22:9-65
	android:name
		ADDED from C:\Muslim Core\app\src\main\AndroidManifest.xml:20:9-46
activity#com.muslimcore.presentation.MainActivity
ADDED from C:\Muslim Core\app\src\main\AndroidManifest.xml:32:9-41:20
	android:screenOrientation
		ADDED from C:\Muslim Core\app\src\main\AndroidManifest.xml:35:13-49
	android:exported
		ADDED from C:\Muslim Core\app\src\main\AndroidManifest.xml:34:13-36
	android:theme
		ADDED from C:\Muslim Core\app\src\main\AndroidManifest.xml:36:13-64
	android:name
		ADDED from C:\Muslim Core\app\src\main\AndroidManifest.xml:33:13-54
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Muslim Core\app\src\main\AndroidManifest.xml:37:13-40:29
action#android.intent.action.MAIN
ADDED from C:\Muslim Core\app\src\main\AndroidManifest.xml:38:17-69
	android:name
		ADDED from C:\Muslim Core\app\src\main\AndroidManifest.xml:38:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Muslim Core\app\src\main\AndroidManifest.xml:39:17-77
	android:name
		ADDED from C:\Muslim Core\app\src\main\AndroidManifest.xml:39:27-74
service#com.muslimcore.data.services.MuslimCoreMessagingService
ADDED from C:\Muslim Core\app\src\main\AndroidManifest.xml:46:9-52:19
	android:exported
		ADDED from C:\Muslim Core\app\src\main\AndroidManifest.xml:48:13-37
	android:name
		ADDED from C:\Muslim Core\app\src\main\AndroidManifest.xml:47:13-69
intent-filter#action:name:com.google.firebase.MESSAGING_EVENT
ADDED from C:\Muslim Core\app\src\main\AndroidManifest.xml:49:13-51:29
action#com.google.firebase.MESSAGING_EVENT
ADDED from C:\Muslim Core\app\src\main\AndroidManifest.xml:50:17-78
	android:name
		ADDED from C:\Muslim Core\app\src\main\AndroidManifest.xml:50:25-75
activity#com.muslimcore.presentation.activities.PrayerAlertActivity
ADDED from C:\Muslim Core\app\src\main\AndroidManifest.xml:55:9-60:67
	android:screenOrientation
		ADDED from C:\Muslim Core\app\src\main\AndroidManifest.xml:59:13-49
	android:launchMode
		ADDED from C:\Muslim Core\app\src\main\AndroidManifest.xml:58:13-43
	android:exported
		ADDED from C:\Muslim Core\app\src\main\AndroidManifest.xml:57:13-37
	android:theme
		ADDED from C:\Muslim Core\app\src\main\AndroidManifest.xml:60:13-64
	android:name
		ADDED from C:\Muslim Core\app\src\main\AndroidManifest.xml:56:13-72
service#com.muslimcore.presentation.services.PrayerService
ADDED from C:\Muslim Core\app\src\main\AndroidManifest.xml:63:9-67:58
	android:enabled
		ADDED from C:\Muslim Core\app\src\main\AndroidManifest.xml:65:13-35
	android:exported
		ADDED from C:\Muslim Core\app\src\main\AndroidManifest.xml:66:13-37
	android:foregroundServiceType
		ADDED from C:\Muslim Core\app\src\main\AndroidManifest.xml:67:13-55
	android:name
		ADDED from C:\Muslim Core\app\src\main\AndroidManifest.xml:64:13-64
service#com.muslimcore.data.services.PrayerNotificationService
ADDED from C:\Muslim Core\app\src\main\AndroidManifest.xml:70:9-73:40
	android:enabled
		ADDED from C:\Muslim Core\app\src\main\AndroidManifest.xml:72:13-35
	android:exported
		ADDED from C:\Muslim Core\app\src\main\AndroidManifest.xml:73:13-37
	android:name
		ADDED from C:\Muslim Core\app\src\main\AndroidManifest.xml:71:13-68
receiver#com.muslimcore.presentation.receivers.BootReceiver
ADDED from C:\Muslim Core\app\src\main\AndroidManifest.xml:76:9-86:20
	android:enabled
		ADDED from C:\Muslim Core\app\src\main\AndroidManifest.xml:78:13-35
	android:exported
		ADDED from C:\Muslim Core\app\src\main\AndroidManifest.xml:79:13-36
	android:name
		ADDED from C:\Muslim Core\app\src\main\AndroidManifest.xml:77:13-64
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.MY_PACKAGE_REPLACED+action:name:android.intent.action.PACKAGE_REPLACED+data:scheme:package
ADDED from C:\Muslim Core\app\src\main\AndroidManifest.xml:80:13-85:29
	android:priority
		ADDED from C:\Muslim Core\app\src\main\AndroidManifest.xml:80:28-51
action#android.intent.action.BOOT_COMPLETED
ADDED from C:\Muslim Core\app\src\main\AndroidManifest.xml:81:17-79
	android:name
		ADDED from C:\Muslim Core\app\src\main\AndroidManifest.xml:81:25-76
action#android.intent.action.MY_PACKAGE_REPLACED
ADDED from C:\Muslim Core\app\src\main\AndroidManifest.xml:82:17-84
	android:name
		ADDED from C:\Muslim Core\app\src\main\AndroidManifest.xml:82:25-81
action#android.intent.action.PACKAGE_REPLACED
ADDED from C:\Muslim Core\app\src\main\AndroidManifest.xml:83:17-81
	android:name
		ADDED from C:\Muslim Core\app\src\main\AndroidManifest.xml:83:25-78
data
ADDED from C:\Muslim Core\app\src\main\AndroidManifest.xml:84:17-50
	android:scheme
		ADDED from C:\Muslim Core\app\src\main\AndroidManifest.xml:84:23-47
receiver#com.muslimcore.data.receivers.PrayerAlarmReceiver
ADDED from C:\Muslim Core\app\src\main\AndroidManifest.xml:89:9-92:40
	android:enabled
		ADDED from C:\Muslim Core\app\src\main\AndroidManifest.xml:91:13-35
	android:exported
		ADDED from C:\Muslim Core\app\src\main\AndroidManifest.xml:92:13-37
	android:name
		ADDED from C:\Muslim Core\app\src\main\AndroidManifest.xml:90:13-63
meta-data#androidx.work.WorkManagerInitializer
ADDED from C:\Muslim Core\app\src\main\AndroidManifest.xml:100:13-103:39
REJECTED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:34:13-36:52
	tools:node
		ADDED from C:\Muslim Core\app\src\main\AndroidManifest.xml:103:17-36
	android:value
		ADDED from C:\Muslim Core\app\src\main\AndroidManifest.xml:102:17-49
	android:name
		ADDED from C:\Muslim Core\app\src\main\AndroidManifest.xml:101:17-68
uses-sdk
INJECTED from C:\Muslim Core\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Muslim Core\app\src\main\AndroidManifest.xml
INJECTED from C:\Muslim Core\app\src\main\AndroidManifest.xml
MERGED from [androidx.databinding:databinding-adapters:8.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\ce93ce5bd45704ff32cf7f9b33dbc2e2\transformed\databinding-adapters-8.1.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-adapters:8.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\ce93ce5bd45704ff32cf7f9b33dbc2e2\transformed\databinding-adapters-8.1.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-ktx:8.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\d3d8e8d40fcc52a7ca567090c9bb9584\transformed\databinding-ktx-8.1.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-ktx:8.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\d3d8e8d40fcc52a7ca567090c9bb9584\transformed\databinding-ktx-8.1.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-runtime:8.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\91f5ac35e2aac1e5ec49fa8326c1819a\transformed\databinding-runtime-8.1.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-runtime:8.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\91f5ac35e2aac1e5ec49fa8326c1819a\transformed\databinding-runtime-8.1.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\21bd60ff35e03baea11470532ebdf25c\transformed\viewbinding-8.1.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\21bd60ff35e03baea11470532ebdf25c\transformed\viewbinding-8.1.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.preference:preference-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\883ed09f89aea110c50d59ef0b3bce1d\transformed\preference-ktx-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\883ed09f89aea110c50d59ef0b3bce1d\transformed\preference-ktx-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.navigation:navigation-common:2.7.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\eea476f82e6112e83a24b6c42880b608\transformed\navigation-common-2.7.4\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.7.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\eea476f82e6112e83a24b6c42880b608\transformed\navigation-common-2.7.4\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\4643f83ad22a1c94dcdf8fdea70cbfd7\transformed\navigation-common-ktx-2.7.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\4643f83ad22a1c94dcdf8fdea70cbfd7\transformed\navigation-common-ktx-2.7.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\69000e4e45894c9884b32a91ee0b736c\transformed\navigation-runtime-2.7.4\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\69000e4e45894c9884b32a91ee0b736c\transformed\navigation-runtime-2.7.4\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-ui:2.7.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\cacdfc042dfae4e43c008dc67295820e\transformed\navigation-ui-2.7.4\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-ui:2.7.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\cacdfc042dfae4e43c008dc67295820e\transformed\navigation-ui-2.7.4\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\f96b327603e054eec2a5940deb11cfd0\transformed\navigation-runtime-ktx-2.7.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\f96b327603e054eec2a5940deb11cfd0\transformed\navigation-runtime-ktx-2.7.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui-ktx:2.7.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\2c9ea3e49a19606d7ec080362802a64a\transformed\navigation-ui-ktx-2.7.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui-ktx:2.7.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\2c9ea3e49a19606d7ec080362802a64a\transformed\navigation-ui-ktx-2.7.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-fragment-ktx:2.7.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\79f7d1071fac12787ccb18c3f9960464\transformed\navigation-fragment-ktx-2.7.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-fragment-ktx:2.7.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\79f7d1071fac12787ccb18c3f9960464\transformed\navigation-fragment-ktx-2.7.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-fragment:2.7.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\f2e0173b205ab009bcb83bc37b0023a7\transformed\navigation-fragment-2.7.4\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-fragment:2.7.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\f2e0173b205ab009bcb83bc37b0023a7\transformed\navigation-fragment-2.7.4\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\23d7c820469d7d72a3f613bbedf53593\transformed\material-1.10.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\23d7c820469d7d72a3f613bbedf53593\transformed\material-1.10.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\fd9e756f56bf090cd291cb3d53d819c4\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\fd9e756f56bf090cd291cb3d53d819c4\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [com.airbnb.android:lottie:6.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc7a596ca09ec1789d8b31b6cf9b156f\transformed\lottie-6.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.airbnb.android:lottie:6.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc7a596ca09ec1789d8b31b6cf9b156f\transformed\lottie-6.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\527f5e9a80e364e458d082bf65a46dd8\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\527f5e9a80e364e458d082bf65a46dd8\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\2025416948d6099286a1c5deac9fc59b\transformed\preference-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\2025416948d6099286a1c5deac9fc59b\transformed\preference-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e6699e020abbc856e9d4962d528675a8\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e6699e020abbc856e9d4962d528675a8\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.dagger:hilt-android:2.48] C:\Users\<USER>\.gradle\caches\8.12\transforms\3206c75f50878f9df80e75eb8148db71\transformed\hilt-android-2.48\AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:hilt-android:2.48] C:\Users\<USER>\.gradle\caches\8.12\transforms\3206c75f50878f9df80e75eb8148db71\transformed\hilt-android-2.48\AndroidManifest.xml:18:3-42
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ce2d8fddf65a1d1c885d98f45b4a48f\transformed\play-services-maps-18.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ce2d8fddf65a1d1c885d98f45b4a48f\transformed\play-services-maps-18.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f603684ac85a3923c1b73c512f406c56\transformed\glide-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f603684ac85a3923c1b73c512f406c56\transformed\glide-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\332273db20fb8075f00b021e7f25ead7\transformed\play-services-location-21.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\332273db20fb8075f00b021e7f25ead7\transformed\play-services-location-21.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-firestore-ktx:24.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\6cb3da3a0e28fbb3430bbd354b98bd68\transformed\firebase-firestore-ktx-24.9.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-firestore-ktx:24.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\6cb3da3a0e28fbb3430bbd354b98bd68\transformed\firebase-firestore-ktx-24.9.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-firestore:24.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\bc763f29c7783602ce54f376238f5573\transformed\firebase-firestore-24.9.1\AndroidManifest.xml:6:5-44
MERGED from [com.google.firebase:firebase-firestore:24.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\bc763f29c7783602ce54f376238f5573\transformed\firebase-firestore-24.9.1\AndroidManifest.xml:6:5-44
MERGED from [com.google.firebase:firebase-messaging-ktx:23.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f02b071850434d6569dac072ad31c2fd\transformed\firebase-messaging-ktx-23.3.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-messaging-ktx:23.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f02b071850434d6569dac072ad31c2fd\transformed\firebase-messaging-ktx-23.3.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-messaging:23.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\657d4b970ca786a19c67b9546b583e6a\transformed\firebase-messaging-23.3.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-messaging:23.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\657d4b970ca786a19c67b9546b583e6a\transformed\firebase-messaging-23.3.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-auth-ktx:22.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a121f920b99ddcf8e2e1aac0ac05b0f9\transformed\firebase-auth-ktx-22.3.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth-ktx:22.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a121f920b99ddcf8e2e1aac0ac05b0f9\transformed\firebase-auth-ktx-22.3.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fe4f96e904e00d58c3ce6afc4255acc8\transformed\firebase-auth-22.3.0\AndroidManifest.xml:21:5-23:64
MERGED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fe4f96e904e00d58c3ce6afc4255acc8\transformed\firebase-auth-22.3.0\AndroidManifest.xml:21:5-23:64
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7ec7858ef834c086f5b58a24ad7fd8fe\transformed\firebase-appcheck-interop-17.0.0\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7ec7858ef834c086f5b58a24ad7fd8fe\transformed\firebase-appcheck-interop-17.0.0\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\4086e7d05b484f7ea43cbb5ea3d43a01\transformed\firebase-database-collection-18.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\4086e7d05b484f7ea43cbb5ea3d43a01\transformed\firebase-database-collection-18.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.android.gms:play-services-auth-api-phone:17.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a539898345436c8142f74151471946dd\transformed\play-services-auth-api-phone-17.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:17.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a539898345436c8142f74151471946dd\transformed\play-services-auth-api-phone-17.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bd229cba7731028eaebcf7d452c4e6dc\transformed\play-services-base-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bd229cba7731028eaebcf7d452c4e6dc\transformed\play-services-base-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.firebase:firebase-config-ktx:21.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1a29fdd56c9ef7f7a8d2faae48bfc03d\transformed\firebase-config-ktx-21.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-config-ktx:21.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1a29fdd56c9ef7f7a8d2faae48bfc03d\transformed\firebase-config-ktx-21.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\462349714f38d054982a20853c15eade\transformed\firebase-analytics-ktx-21.5.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\462349714f38d054982a20853c15eade\transformed\firebase-analytics-ktx-21.5.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-config:21.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7d7bd6579bd35503adb2fe395d71ea3e\transformed\firebase-config-21.6.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-config:21.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7d7bd6579bd35503adb2fe395d71ea3e\transformed\firebase-config-21.6.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-analytics:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\22ba9d1314a24d427d816c3b92a77350\transformed\firebase-analytics-21.5.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-analytics:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\22ba9d1314a24d427d816c3b92a77350\transformed\firebase-analytics-21.5.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c42e3f53f251f72190ffef6945138b19\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c42e3f53f251f72190ffef6945138b19\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4550ebb1f57cdacda2347951a8b355e7\transformed\firebase-installations-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4550ebb1f57cdacda2347951a8b355e7\transformed\firebase-installations-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\ce87eaf2450b4f0f51a49634d42a1887\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\ce87eaf2450b4f0f51a49634d42a1887\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5f697a4a3151cf4487d51aef6b9528c1\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5f697a4a3151cf4487d51aef6b9528c1\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\29a9afe92f618d0e0b754a9bfb76a586\transformed\firebase-common-20.4.2\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\29a9afe92f618d0e0b754a9bfb76a586\transformed\firebase-common-20.4.2\AndroidManifest.xml:19:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7cda915d6bf6539208b46b00879d8712\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7cda915d6bf6539208b46b00879d8712\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\39a8a2aca2bad19af9fb513e99abd016\transformed\emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\39a8a2aca2bad19af9fb513e99abd016\transformed\emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\8.12\transforms\e9053bce3f72d4aea6ccbcff5e628126\transformed\viewpager2-1.1.0-beta02\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\8.12\transforms\e9053bce3f72d4aea6ccbcff5e628126\transformed\viewpager2-1.1.0-beta02\AndroidManifest.xml:5:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\dea3e6fcf5697c9423505c2045e63399\transformed\recyclerview-1.3.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\dea3e6fcf5697c9423505c2045e63399\transformed\recyclerview-1.3.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fb1d9df47d7b984f4dadfbaace818bfe\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fb1d9df47d7b984f4dadfbaace818bfe\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a9ad9f627cca6e719a539b990757f1a8\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a9ad9f627cca6e719a539b990757f1a8\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\2909155116065c1feba18126652ab8b1\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\2909155116065c1feba18126652ab8b1\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f2278bd78dd5b8de51d11a4c518bddc3\transformed\activity-1.8.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f2278bd78dd5b8de51d11a4c518bddc3\transformed\activity-1.8.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b3220947d7de38838cf340bae783eebe\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b3220947d7de38838cf340bae783eebe\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2bcf7493bd089a2ab471d9e5de7b9724\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2bcf7493bd089a2ab471d9e5de7b9724\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.work:work-runtime-ktx:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\610865b8ffab8bd44e532006577a1196\transformed\work-runtime-ktx-2.8.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime-ktx:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\610865b8ffab8bd44e532006577a1196\transformed\work-runtime-ktx-2.8.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dbcda8e42cd24d91c642ef6749aeeaff\transformed\lifecycle-service-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dbcda8e42cd24d91c642ef6749aeeaff\transformed\lifecycle-service-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2e7b31cf85e628a427d204ca3c1e1d04\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2e7b31cf85e628a427d204ca3c1e1d04\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4cce030851b64fce14a30f35636cafff\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4cce030851b64fce14a30f35636cafff\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2054581ecb8aae4e1f94cbef4ff3eeed\transformed\play-services-measurement-sdk-21.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2054581ecb8aae4e1f94cbef4ff3eeed\transformed\play-services-measurement-sdk-21.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\08cd672f2bf9942df1c4039c23a8420a\transformed\play-services-measurement-impl-21.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\08cd672f2bf9942df1c4039c23a8420a\transformed\play-services-measurement-impl-21.5.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.12\transforms\6767009578d7e0362def9a7abe632d78\transformed\ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.12\transforms\6767009578d7e0362def9a7abe632d78\transformed\ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.12\transforms\135d01c71a961225103e48b7dd206008\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:20:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.12\transforms\135d01c71a961225103e48b7dd206008\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6d2285c0a3d448473ee7ff90d5118c5a\transformed\core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6d2285c0a3d448473ee7ff90d5118c5a\transformed\core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7c41441c1adccc084d55e76797449120\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7c41441c1adccc084d55e76797449120\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\2829f477d9c6056a40040b143f304a8b\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\2829f477d9c6056a40040b143f304a8b\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b6d17d87dae82d892d5ed1b2c35b7a41\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b6d17d87dae82d892d5ed1b2c35b7a41\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\999abc7c220f9e0685ae9357592681d0\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\999abc7c220f9e0685ae9357592681d0\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b7dfa5d4184699e0b717fb7b27565e40\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b7dfa5d4184699e0b717fb7b27565e40\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d78166393493a751715ec2d1827d3b3c\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d78166393493a751715ec2d1827d3b3c\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fca31f41437b434ebf5863b0af1ea5a7\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fca31f41437b434ebf5863b0af1ea5a7\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\03584becdb334b030db50442e94ad5c3\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\03584becdb334b030db50442e94ad5c3\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\775b0f6bc0598e3fa176fb3ab128693d\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\775b0f6bc0598e3fa176fb3ab128693d\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\36f2737b82835d7118612a5b23b076a1\transformed\play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\36f2737b82835d7118612a5b23b076a1\transformed\play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b018e785c25bf37ddd4e8ab57d8834e7\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b018e785c25bf37ddd4e8ab57d8834e7\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7b69551ac5e8933639210b54b8e1f4b0\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7b69551ac5e8933639210b54b8e1f4b0\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.browser:browser:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\81561af282a29cce9274e37ca4fb03dd\transformed\browser-1.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.browser:browser:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\81561af282a29cce9274e37ca4fb03dd\transformed\browser-1.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5ce941577190a4b7e40da0e194a20c68\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5ce941577190a4b7e40da0e194a20c68\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e661d63d9c04259b2495b443a4caf917\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e661d63d9c04259b2495b443a4caf917\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\29aa2070c5f46de89120ba6e20f2606d\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\29aa2070c5f46de89120ba6e20f2606d\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8c3942081edffb4b875bc5f4359e13d8\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8c3942081edffb4b875bc5f4359e13d8\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\683da4f7ef5696e7dcd83bd164cc9897\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\683da4f7ef5696e7dcd83bd164cc9897\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\52cfa56fd2ad7827c32e9e051048beda\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\52cfa56fd2ad7827c32e9e051048beda\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\382da343d6fc045de39887588a3cc4a8\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\382da343d6fc045de39887588a3cc4a8\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.recaptcha:recaptcha:18.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\9ceca76805a3be07b1bd347340a44134\transformed\recaptcha-18.1.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.recaptcha:recaptcha:18.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\9ceca76805a3be07b1bd347340a44134\transformed\recaptcha-18.1.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\47a09af950e9d4633e43ca23a3c9a0bb\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\47a09af950e9d4633e43ca23a3c9a0bb\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [com.google.android.play:integrity:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b0cd884fbf9a6450705aeb223448a38c\transformed\integrity-1.1.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.android.play:integrity:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b0cd884fbf9a6450705aeb223448a38c\transformed\integrity-1.1.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fb4647b322715c176135d86e4e428266\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fb4647b322715c176135d86e4e428266\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\516259ec442d5b9ec5e79b98a8050845\transformed\play-services-cloud-messaging-17.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\516259ec442d5b9ec5e79b98a8050845\transformed\play-services-cloud-messaging-17.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\a0eb2e2a12f92faafc8f0b774c5db902\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\a0eb2e2a12f92faafc8f0b774c5db902\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:4:5-43
MERGED from [com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5565bd1e3d8bedbb48c3d9ebd009d2d6\transformed\firebase-abt-21.1.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5565bd1e3d8bedbb48c3d9ebd009d2d6\transformed\firebase-abt-21.1.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4eb2364817c7d34bb93acc989f7bd3db\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4eb2364817c7d34bb93acc989f7bd3db\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cb231c7e35432be62c560b860c915b21\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cb231c7e35432be62c560b860c915b21\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2e44da5bd1abc324905da37f6753f3bb\transformed\play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2e44da5bd1abc324905da37f6753f3bb\transformed\play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1239f049c4746005cd1af317bc2e4ecd\transformed\play-services-measurement-base-21.5.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1239f049c4746005cd1af317bc2e4ecd\transformed\play-services-measurement-base-21.5.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\812136ae31eafce15fafbc5dfed6f6ac\transformed\play-services-basement-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\812136ae31eafce15fafbc5dfed6f6ac\transformed\play-services-basement-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\14523b378ddeb224f3413513055a3ace\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\14523b378ddeb224f3413513055a3ace\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\859caf9af04706cbb82a4303b043aa18\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\859caf9af04706cbb82a4303b043aa18\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1a669755bc0544f538ac3665dbb69afc\transformed\activity-ktx-1.8.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1a669755bc0544f538ac3665dbb69afc\transformed\activity-ktx-1.8.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0ca9a8cd02d7bb1dc6fa2bacc70e914e\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0ca9a8cd02d7bb1dc6fa2bacc70e914e\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\93023eb18092c846c4a0d8a59ef6d371\transformed\lifecycle-livedata-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\93023eb18092c846c4a0d8a59ef6d371\transformed\lifecycle-livedata-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ce0a286f5a517a5411a0f92eb45a7234\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ce0a286f5a517a5411a0f92eb45a7234\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.room:room-runtime:2.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\baa7f398d5d0d1385b12ae8bfc214adc\transformed\room-runtime-2.6.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\baa7f398d5d0d1385b12ae8bfc214adc\transformed\room-runtime-2.6.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-ktx:2.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\defb090a73c05d9bd9af7eac4735961f\transformed\room-ktx-2.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\defb090a73c05d9bd9af7eac4735961f\transformed\room-ktx-2.6.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-config-interop:16.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\33b14fe4b141a0d93205f4ffe3f88514\transformed\firebase-config-interop-16.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-config-interop:16.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\33b14fe4b141a0d93205f4ffe3f88514\transformed\firebase-config-interop-16.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\3ca3bb862412792bd96083ec00cacda6\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\3ca3bb862412792bd96083ec00cacda6\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.12\transforms\683f793d9b7aa5372f5619ebe6359687\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.12\transforms\683f793d9b7aa5372f5619ebe6359687\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a74e6f76ec28a257e238d2e8697e5c8\transformed\firebase-encoders-json-18.0.1\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a74e6f76ec28a257e238d2e8697e5c8\transformed\firebase-encoders-json-18.0.1\AndroidManifest.xml:18:5-20:41
MERGED from [androidx.core:core-splashscreen:1.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cdebcb5cea68a5cacee791078fa3b341\transformed\core-splashscreen-1.0.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core-splashscreen:1.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cdebcb5cea68a5cacee791078fa3b341\transformed\core-splashscreen-1.0.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.12\transforms\4d198d6f53404638e45dff19d597f6a7\transformed\transport-runtime-3.1.8\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.12\transforms\4d198d6f53404638e45dff19d597f6a7\transformed\transport-runtime-3.1.8\AndroidManifest.xml:18:5-20:41
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a39ac11765bd508aeac5f649e35b4c9\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a39ac11765bd508aeac5f649e35b4c9\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ab9ce3d6fabc773074c27a4a16889e6f\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ab9ce3d6fabc773074c27a4a16889e6f\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\ebcadbd030c7c25414955d10f0fdf41b\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\ebcadbd030c7c25414955d10f0fdf41b\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9855521e152600f5c73ac08925a289ee\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9855521e152600f5c73ac08925a289ee\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f377f2617dbea8c7b9f73ab7b4573b28\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f377f2617dbea8c7b9f73ab7b4573b28\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dbf57c3e0f51ef1f88e5786411429f26\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dbf57c3e0f51ef1f88e5786411429f26\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2b73d759b335a274cdc83dddb7f4dbe3\transformed\gifdecoder-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2b73d759b335a274cdc83dddb7f4dbe3\transformed\gifdecoder-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\067a0fabd78bbf4cca26a52ee7c34f16\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\067a0fabd78bbf4cca26a52ee7c34f16\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ce2fbb511b1dd1207b7023c44f05bb81\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ce2fbb511b1dd1207b7023c44f05bb81\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f059acc78cb16502a6170c70ab7b48a3\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f059acc78cb16502a6170c70ab7b48a3\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9dc23eb98257d1a42135c42113fc6bcc\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9dc23eb98257d1a42135c42113fc6bcc\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\13d57602bd55674a929e79ea990e7f02\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\13d57602bd55674a929e79ea990e7f02\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-components:17.1.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\6e73913549ad530644c8e168ab1e59d6\transformed\firebase-components-17.1.5\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-components:17.1.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\6e73913549ad530644c8e168ab1e59d6\transformed\firebase-components-17.1.5\AndroidManifest.xml:18:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\00239ce5fe5e25cd05268188b7bd15c1\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\00239ce5fe5e25cd05268188b7bd15c1\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.datatransport:transport-api:3.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\af399a98b4fd4de64551879c9099860e\transformed\transport-api-3.0.0\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-api:3.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\af399a98b4fd4de64551879c9099860e\transformed\transport-api-3.0.0\AndroidManifest.xml:18:5-20:41
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a12e2f63101da8b7e81b22f914720ee3\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a12e2f63101da8b7e81b22f914720ee3\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\63d7229498fc9f109441ea9f24dad33a\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\63d7229498fc9f109441ea9f24dad33a\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8cac734f3b70a6524ee8666ddab0ba00\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8cac734f3b70a6524ee8666ddab0ba00\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [io.grpc:grpc-android:1.52.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\6c03df68e93751f100c940675ea19a00\transformed\grpc-android-1.52.1\AndroidManifest.xml:5:5-7:41
MERGED from [io.grpc:grpc-android:1.52.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\6c03df68e93751f100c940675ea19a00\transformed\grpc-android-1.52.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.dagger:dagger-lint-aar:2.48] C:\Users\<USER>\.gradle\caches\8.12\transforms\69aa2ec88c4b2e954a0ccb3c5ea074a1\transformed\dagger-lint-aar-2.48\AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:dagger-lint-aar:2.48] C:\Users\<USER>\.gradle\caches\8.12\transforms\69aa2ec88c4b2e954a0ccb3c5ea074a1\transformed\dagger-lint-aar-2.48\AndroidManifest.xml:18:3-42
MERGED from [com.google.firebase:protolite-well-known-types:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dfa0dd3b089da752fa5e8b6008302b33\transformed\protolite-well-known-types-18.0.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.firebase:protolite-well-known-types:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dfa0dd3b089da752fa5e8b6008302b33\transformed\protolite-well-known-types-18.0.0\AndroidManifest.xml:7:5-9:41
	tools:overrideLibrary
		ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fe4f96e904e00d58c3ce6afc4255acc8\transformed\firebase-auth-22.3.0\AndroidManifest.xml:23:9-61
	android:targetSdkVersion
		INJECTED from C:\Muslim Core\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Muslim Core\app\src\main\AndroidManifest.xml
uses-feature#0x00020000
ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ce2d8fddf65a1d1c885d98f45b4a48f\transformed\play-services-maps-18.2.0\AndroidManifest.xml:26:5-28:35
	android:glEsVersion
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ce2d8fddf65a1d1c885d98f45b4a48f\transformed\play-services-maps-18.2.0\AndroidManifest.xml:27:9-41
	android:required
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ce2d8fddf65a1d1c885d98f45b4a48f\transformed\play-services-maps-18.2.0\AndroidManifest.xml:28:9-32
queries
ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ce2d8fddf65a1d1c885d98f45b4a48f\transformed\play-services-maps-18.2.0\AndroidManifest.xml:30:5-34:15
package#com.google.android.apps.maps
ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ce2d8fddf65a1d1c885d98f45b4a48f\transformed\play-services-maps-18.2.0\AndroidManifest.xml:33:9-64
	android:name
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ce2d8fddf65a1d1c885d98f45b4a48f\transformed\play-services-maps-18.2.0\AndroidManifest.xml:33:18-61
uses-library#org.apache.http.legacy
ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ce2d8fddf65a1d1c885d98f45b4a48f\transformed\play-services-maps-18.2.0\AndroidManifest.xml:39:9-41:40
	android:required
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ce2d8fddf65a1d1c885d98f45b4a48f\transformed\play-services-maps-18.2.0\AndroidManifest.xml:41:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ce2d8fddf65a1d1c885d98f45b4a48f\transformed\play-services-maps-18.2.0\AndroidManifest.xml:40:13-50
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [com.google.firebase:firebase-firestore-ktx:24.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\6cb3da3a0e28fbb3430bbd354b98bd68\transformed\firebase-firestore-ktx-24.9.1\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-firestore:24.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\bc763f29c7783602ce54f376238f5573\transformed\firebase-firestore-24.9.1\AndroidManifest.xml:14:9-23:19
MERGED from [com.google.firebase:firebase-firestore:24.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\bc763f29c7783602ce54f376238f5573\transformed\firebase-firestore-24.9.1\AndroidManifest.xml:14:9-23:19
MERGED from [com.google.firebase:firebase-messaging-ktx:23.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f02b071850434d6569dac072ad31c2fd\transformed\firebase-messaging-ktx-23.3.1\AndroidManifest.xml:23:9-29:19
MERGED from [com.google.firebase:firebase-messaging-ktx:23.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f02b071850434d6569dac072ad31c2fd\transformed\firebase-messaging-ktx-23.3.1\AndroidManifest.xml:23:9-29:19
MERGED from [com.google.firebase:firebase-messaging:23.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\657d4b970ca786a19c67b9546b583e6a\transformed\firebase-messaging-23.3.1\AndroidManifest.xml:54:9-63:19
MERGED from [com.google.firebase:firebase-messaging:23.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\657d4b970ca786a19c67b9546b583e6a\transformed\firebase-messaging-23.3.1\AndroidManifest.xml:54:9-63:19
MERGED from [com.google.firebase:firebase-auth-ktx:22.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a121f920b99ddcf8e2e1aac0ac05b0f9\transformed\firebase-auth-ktx-22.3.0\AndroidManifest.xml:8:9-14:19
MERGED from [com.google.firebase:firebase-auth-ktx:22.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a121f920b99ddcf8e2e1aac0ac05b0f9\transformed\firebase-auth-ktx-22.3.0\AndroidManifest.xml:8:9-14:19
MERGED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fe4f96e904e00d58c3ce6afc4255acc8\transformed\firebase-auth-22.3.0\AndroidManifest.xml:66:9-72:19
MERGED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fe4f96e904e00d58c3ce6afc4255acc8\transformed\firebase-auth-22.3.0\AndroidManifest.xml:66:9-72:19
MERGED from [com.google.firebase:firebase-config-ktx:21.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1a29fdd56c9ef7f7a8d2faae48bfc03d\transformed\firebase-config-ktx-21.6.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-config-ktx:21.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1a29fdd56c9ef7f7a8d2faae48bfc03d\transformed\firebase-config-ktx-21.6.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\462349714f38d054982a20853c15eade\transformed\firebase-analytics-ktx-21.5.0\AndroidManifest.xml:8:9-14:19
MERGED from [com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\462349714f38d054982a20853c15eade\transformed\firebase-analytics-ktx-21.5.0\AndroidManifest.xml:8:9-14:19
MERGED from [com.google.firebase:firebase-config:21.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7d7bd6579bd35503adb2fe395d71ea3e\transformed\firebase-config-21.6.0\AndroidManifest.xml:26:9-35:19
MERGED from [com.google.firebase:firebase-config:21.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7d7bd6579bd35503adb2fe395d71ea3e\transformed\firebase-config-21.6.0\AndroidManifest.xml:26:9-35:19
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c42e3f53f251f72190ffef6945138b19\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:34:9-40:19
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c42e3f53f251f72190ffef6945138b19\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:34:9-40:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4550ebb1f57cdacda2347951a8b355e7\transformed\firebase-installations-17.2.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4550ebb1f57cdacda2347951a8b355e7\transformed\firebase-installations-17.2.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\ce87eaf2450b4f0f51a49634d42a1887\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\ce87eaf2450b4f0f51a49634d42a1887\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\29a9afe92f618d0e0b754a9bfb76a586\transformed\firebase-common-20.4.2\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\29a9afe92f618d0e0b754a9bfb76a586\transformed\firebase-common-20.4.2\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5565bd1e3d8bedbb48c3d9ebd009d2d6\transformed\firebase-abt-21.1.1\AndroidManifest.xml:11:9-15:19
MERGED from [com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5565bd1e3d8bedbb48c3d9ebd009d2d6\transformed\firebase-abt-21.1.1\AndroidManifest.xml:11:9-15:19
MERGED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\3ca3bb862412792bd96083ec00cacda6\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:24:9-30:19
MERGED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\3ca3bb862412792bd96083ec00cacda6\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:24:9-30:19
	android:exported
		ADDED from [com.google.firebase:firebase-firestore-ktx:24.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\6cb3da3a0e28fbb3430bbd354b98bd68\transformed\firebase-firestore-ktx-24.9.1\AndroidManifest.xml:11:13-37
	tools:targetApi
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\29a9afe92f618d0e0b754a9bfb76a586\transformed\firebase-common-20.4.2\AndroidManifest.xml:34:13-32
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\29a9afe92f618d0e0b754a9bfb76a586\transformed\firebase-common-20.4.2\AndroidManifest.xml:32:13-43
	android:name
		ADDED from [com.google.firebase:firebase-firestore-ktx:24.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\6cb3da3a0e28fbb3430bbd354b98bd68\transformed\firebase-firestore-ktx-24.9.1\AndroidManifest.xml:10:13-84
meta-data#com.google.firebase.components:com.google.firebase.firestore.ktx.FirebaseFirestoreLegacyRegistrar
ADDED from [com.google.firebase:firebase-firestore-ktx:24.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\6cb3da3a0e28fbb3430bbd354b98bd68\transformed\firebase-firestore-ktx-24.9.1\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-firestore-ktx:24.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\6cb3da3a0e28fbb3430bbd354b98bd68\transformed\firebase-firestore-ktx-24.9.1\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-firestore-ktx:24.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\6cb3da3a0e28fbb3430bbd354b98bd68\transformed\firebase-firestore-ktx-24.9.1\AndroidManifest.xml:13:17-129
meta-data#com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar
ADDED from [com.google.firebase:firebase-firestore:24.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\bc763f29c7783602ce54f376238f5573\transformed\firebase-firestore-24.9.1\AndroidManifest.xml:17:13-19:85
	android:value
		ADDED from [com.google.firebase:firebase-firestore:24.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\bc763f29c7783602ce54f376238f5573\transformed\firebase-firestore-24.9.1\AndroidManifest.xml:19:17-82
	android:name
		ADDED from [com.google.firebase:firebase-firestore:24.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\bc763f29c7783602ce54f376238f5573\transformed\firebase-firestore-24.9.1\AndroidManifest.xml:18:17-122
meta-data#com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar
ADDED from [com.google.firebase:firebase-firestore:24.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\bc763f29c7783602ce54f376238f5573\transformed\firebase-firestore-24.9.1\AndroidManifest.xml:20:13-22:85
	android:value
		ADDED from [com.google.firebase:firebase-firestore:24.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\bc763f29c7783602ce54f376238f5573\transformed\firebase-firestore-24.9.1\AndroidManifest.xml:22:17-82
	android:name
		ADDED from [com.google.firebase:firebase-firestore:24.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\bc763f29c7783602ce54f376238f5573\transformed\firebase-firestore-24.9.1\AndroidManifest.xml:21:17-111
meta-data#com.google.firebase.components:com.google.firebase.messaging.ktx.FirebaseMessagingLegacyRegistrar
ADDED from [com.google.firebase:firebase-messaging-ktx:23.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f02b071850434d6569dac072ad31c2fd\transformed\firebase-messaging-ktx-23.3.1\AndroidManifest.xml:26:13-28:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging-ktx:23.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f02b071850434d6569dac072ad31c2fd\transformed\firebase-messaging-ktx-23.3.1\AndroidManifest.xml:28:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging-ktx:23.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f02b071850434d6569dac072ad31c2fd\transformed\firebase-messaging-ktx-23.3.1\AndroidManifest.xml:27:17-129
uses-permission#com.google.android.c2dm.permission.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:23.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\657d4b970ca786a19c67b9546b583e6a\transformed\firebase-messaging-23.3.1\AndroidManifest.xml:26:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\516259ec442d5b9ec5e79b98a8050845\transformed\play-services-cloud-messaging-17.0.1\AndroidManifest.xml:11:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\516259ec442d5b9ec5e79b98a8050845\transformed\play-services-cloud-messaging-17.0.1\AndroidManifest.xml:11:5-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:23.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\657d4b970ca786a19c67b9546b583e6a\transformed\firebase-messaging-23.3.1\AndroidManifest.xml:26:22-79
receiver#com.google.firebase.iid.FirebaseInstanceIdReceiver
ADDED from [com.google.firebase:firebase-messaging:23.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\657d4b970ca786a19c67b9546b583e6a\transformed\firebase-messaging-23.3.1\AndroidManifest.xml:29:9-40:20
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:23.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\657d4b970ca786a19c67b9546b583e6a\transformed\firebase-messaging-23.3.1\AndroidManifest.xml:31:13-36
	android:permission
		ADDED from [com.google.firebase:firebase-messaging:23.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\657d4b970ca786a19c67b9546b583e6a\transformed\firebase-messaging-23.3.1\AndroidManifest.xml:32:13-73
	android:name
		ADDED from [com.google.firebase:firebase-messaging:23.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\657d4b970ca786a19c67b9546b583e6a\transformed\firebase-messaging-23.3.1\AndroidManifest.xml:30:13-78
intent-filter#action:name:com.google.android.c2dm.intent.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:23.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\657d4b970ca786a19c67b9546b583e6a\transformed\firebase-messaging-23.3.1\AndroidManifest.xml:33:13-35:29
action#com.google.android.c2dm.intent.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:23.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\657d4b970ca786a19c67b9546b583e6a\transformed\firebase-messaging-23.3.1\AndroidManifest.xml:34:17-81
	android:name
		ADDED from [com.google.firebase:firebase-messaging:23.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\657d4b970ca786a19c67b9546b583e6a\transformed\firebase-messaging-23.3.1\AndroidManifest.xml:34:25-78
meta-data#com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED
ADDED from [com.google.firebase:firebase-messaging:23.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\657d4b970ca786a19c67b9546b583e6a\transformed\firebase-messaging-23.3.1\AndroidManifest.xml:37:13-39:40
	android:value
		ADDED from [com.google.firebase:firebase-messaging:23.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\657d4b970ca786a19c67b9546b583e6a\transformed\firebase-messaging-23.3.1\AndroidManifest.xml:39:17-37
	android:name
		ADDED from [com.google.firebase:firebase-messaging:23.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\657d4b970ca786a19c67b9546b583e6a\transformed\firebase-messaging-23.3.1\AndroidManifest.xml:38:17-92
service#com.google.firebase.messaging.FirebaseMessagingService
ADDED from [com.google.firebase:firebase-messaging:23.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\657d4b970ca786a19c67b9546b583e6a\transformed\firebase-messaging-23.3.1\AndroidManifest.xml:46:9-53:19
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:23.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\657d4b970ca786a19c67b9546b583e6a\transformed\firebase-messaging-23.3.1\AndroidManifest.xml:49:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-messaging:23.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\657d4b970ca786a19c67b9546b583e6a\transformed\firebase-messaging-23.3.1\AndroidManifest.xml:48:13-43
	android:name
		ADDED from [com.google.firebase:firebase-messaging:23.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\657d4b970ca786a19c67b9546b583e6a\transformed\firebase-messaging-23.3.1\AndroidManifest.xml:47:13-82
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar
ADDED from [com.google.firebase:firebase-messaging:23.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\657d4b970ca786a19c67b9546b583e6a\transformed\firebase-messaging-23.3.1\AndroidManifest.xml:57:13-59:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:23.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\657d4b970ca786a19c67b9546b583e6a\transformed\firebase-messaging-23.3.1\AndroidManifest.xml:59:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:23.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\657d4b970ca786a19c67b9546b583e6a\transformed\firebase-messaging-23.3.1\AndroidManifest.xml:58:17-122
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar
ADDED from [com.google.firebase:firebase-messaging:23.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\657d4b970ca786a19c67b9546b583e6a\transformed\firebase-messaging-23.3.1\AndroidManifest.xml:60:13-62:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:23.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\657d4b970ca786a19c67b9546b583e6a\transformed\firebase-messaging-23.3.1\AndroidManifest.xml:62:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:23.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\657d4b970ca786a19c67b9546b583e6a\transformed\firebase-messaging-23.3.1\AndroidManifest.xml:61:17-119
meta-data#com.google.firebase.components:com.google.firebase.auth.ktx.FirebaseAuthLegacyRegistrar
ADDED from [com.google.firebase:firebase-auth-ktx:22.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a121f920b99ddcf8e2e1aac0ac05b0f9\transformed\firebase-auth-ktx-22.3.0\AndroidManifest.xml:11:13-13:85
	android:value
		ADDED from [com.google.firebase:firebase-auth-ktx:22.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a121f920b99ddcf8e2e1aac0ac05b0f9\transformed\firebase-auth-ktx-22.3.0\AndroidManifest.xml:13:17-82
	android:name
		ADDED from [com.google.firebase:firebase-auth-ktx:22.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a121f920b99ddcf8e2e1aac0ac05b0f9\transformed\firebase-auth-ktx-22.3.0\AndroidManifest.xml:12:17-119
activity#com.google.firebase.auth.internal.GenericIdpActivity
ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fe4f96e904e00d58c3ce6afc4255acc8\transformed\firebase-auth-22.3.0\AndroidManifest.xml:29:9-46:20
	android:excludeFromRecents
		ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fe4f96e904e00d58c3ce6afc4255acc8\transformed\firebase-auth-22.3.0\AndroidManifest.xml:31:13-46
	android:launchMode
		ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fe4f96e904e00d58c3ce6afc4255acc8\transformed\firebase-auth-22.3.0\AndroidManifest.xml:33:13-44
	android:exported
		ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fe4f96e904e00d58c3ce6afc4255acc8\transformed\firebase-auth-22.3.0\AndroidManifest.xml:32:13-36
	android:theme
		ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fe4f96e904e00d58c3ce6afc4255acc8\transformed\firebase-auth-22.3.0\AndroidManifest.xml:34:13-72
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fe4f96e904e00d58c3ce6afc4255acc8\transformed\firebase-auth-22.3.0\AndroidManifest.xml:30:13-80
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:firebase.auth+data:path:/+data:scheme:genericidp
ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fe4f96e904e00d58c3ce6afc4255acc8\transformed\firebase-auth-22.3.0\AndroidManifest.xml:35:13-45:29
action#android.intent.action.VIEW
ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fe4f96e904e00d58c3ce6afc4255acc8\transformed\firebase-auth-22.3.0\AndroidManifest.xml:36:17-69
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fe4f96e904e00d58c3ce6afc4255acc8\transformed\firebase-auth-22.3.0\AndroidManifest.xml:36:25-66
category#android.intent.category.DEFAULT
ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fe4f96e904e00d58c3ce6afc4255acc8\transformed\firebase-auth-22.3.0\AndroidManifest.xml:38:17-76
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fe4f96e904e00d58c3ce6afc4255acc8\transformed\firebase-auth-22.3.0\AndroidManifest.xml:38:27-73
category#android.intent.category.BROWSABLE
ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fe4f96e904e00d58c3ce6afc4255acc8\transformed\firebase-auth-22.3.0\AndroidManifest.xml:39:17-78
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fe4f96e904e00d58c3ce6afc4255acc8\transformed\firebase-auth-22.3.0\AndroidManifest.xml:39:27-75
activity#com.google.firebase.auth.internal.RecaptchaActivity
ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fe4f96e904e00d58c3ce6afc4255acc8\transformed\firebase-auth-22.3.0\AndroidManifest.xml:47:9-64:20
	android:excludeFromRecents
		ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fe4f96e904e00d58c3ce6afc4255acc8\transformed\firebase-auth-22.3.0\AndroidManifest.xml:49:13-46
	android:launchMode
		ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fe4f96e904e00d58c3ce6afc4255acc8\transformed\firebase-auth-22.3.0\AndroidManifest.xml:51:13-44
	android:exported
		ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fe4f96e904e00d58c3ce6afc4255acc8\transformed\firebase-auth-22.3.0\AndroidManifest.xml:50:13-36
	android:theme
		ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fe4f96e904e00d58c3ce6afc4255acc8\transformed\firebase-auth-22.3.0\AndroidManifest.xml:52:13-72
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fe4f96e904e00d58c3ce6afc4255acc8\transformed\firebase-auth-22.3.0\AndroidManifest.xml:48:13-79
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:firebase.auth+data:path:/+data:scheme:recaptcha
ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fe4f96e904e00d58c3ce6afc4255acc8\transformed\firebase-auth-22.3.0\AndroidManifest.xml:53:13-63:29
meta-data#com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar
ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fe4f96e904e00d58c3ce6afc4255acc8\transformed\firebase-auth-22.3.0\AndroidManifest.xml:69:13-71:85
	android:value
		ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fe4f96e904e00d58c3ce6afc4255acc8\transformed\firebase-auth-22.3.0\AndroidManifest.xml:71:17-82
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fe4f96e904e00d58c3ce6afc4255acc8\transformed\firebase-auth-22.3.0\AndroidManifest.xml:70:17-109
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bd229cba7731028eaebcf7d452c4e6dc\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bd229cba7731028eaebcf7d452c4e6dc\transformed\play-services-base-18.1.0\AndroidManifest.xml:22:19-43
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bd229cba7731028eaebcf7d452c4e6dc\transformed\play-services-base-18.1.0\AndroidManifest.xml:21:19-78
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bd229cba7731028eaebcf7d452c4e6dc\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:19-85
meta-data#com.google.firebase.components:com.google.firebase.remoteconfig.ktx.FirebaseConfigLegacyRegistrar
ADDED from [com.google.firebase:firebase-config-ktx:21.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1a29fdd56c9ef7f7a8d2faae48bfc03d\transformed\firebase-config-ktx-21.6.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-config-ktx:21.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1a29fdd56c9ef7f7a8d2faae48bfc03d\transformed\firebase-config-ktx-21.6.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-config-ktx:21.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1a29fdd56c9ef7f7a8d2faae48bfc03d\transformed\firebase-config-ktx-21.6.0\AndroidManifest.xml:13:17-129
meta-data#com.google.firebase.components:com.google.firebase.analytics.ktx.FirebaseAnalyticsLegacyRegistrar
ADDED from [com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\462349714f38d054982a20853c15eade\transformed\firebase-analytics-ktx-21.5.0\AndroidManifest.xml:11:13-13:85
	android:value
		ADDED from [com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\462349714f38d054982a20853c15eade\transformed\firebase-analytics-ktx-21.5.0\AndroidManifest.xml:13:17-82
	android:name
		ADDED from [com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\462349714f38d054982a20853c15eade\transformed\firebase-analytics-ktx-21.5.0\AndroidManifest.xml:12:17-129
meta-data#com.google.firebase.components:com.google.firebase.remoteconfig.FirebaseRemoteConfigKtxRegistrar
ADDED from [com.google.firebase:firebase-config:21.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7d7bd6579bd35503adb2fe395d71ea3e\transformed\firebase-config-21.6.0\AndroidManifest.xml:29:13-31:85
	android:value
		ADDED from [com.google.firebase:firebase-config:21.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7d7bd6579bd35503adb2fe395d71ea3e\transformed\firebase-config-21.6.0\AndroidManifest.xml:31:17-82
	android:name
		ADDED from [com.google.firebase:firebase-config:21.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7d7bd6579bd35503adb2fe395d71ea3e\transformed\firebase-config-21.6.0\AndroidManifest.xml:30:17-128
meta-data#com.google.firebase.components:com.google.firebase.remoteconfig.RemoteConfigRegistrar
ADDED from [com.google.firebase:firebase-config:21.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7d7bd6579bd35503adb2fe395d71ea3e\transformed\firebase-config-21.6.0\AndroidManifest.xml:32:13-34:85
	android:value
		ADDED from [com.google.firebase:firebase-config:21.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7d7bd6579bd35503adb2fe395d71ea3e\transformed\firebase-config-21.6.0\AndroidManifest.xml:34:17-82
	android:name
		ADDED from [com.google.firebase:firebase-config:21.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7d7bd6579bd35503adb2fe395d71ea3e\transformed\firebase-config-21.6.0\AndroidManifest.xml:33:17-117
uses-permission#com.google.android.gms.permission.AD_ID
ADDED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c42e3f53f251f72190ffef6945138b19\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\08cd672f2bf9942df1c4039c23a8420a\transformed\play-services-measurement-impl-21.5.0\AndroidManifest.xml:27:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\08cd672f2bf9942df1c4039c23a8420a\transformed\play-services-measurement-impl-21.5.0\AndroidManifest.xml:27:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cb231c7e35432be62c560b860c915b21\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cb231c7e35432be62c560b860c915b21\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2e44da5bd1abc324905da37f6753f3bb\transformed\play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2e44da5bd1abc324905da37f6753f3bb\transformed\play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:26:5-79
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c42e3f53f251f72190ffef6945138b19\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:25:22-76
uses-permission#android.permission.ACCESS_ADSERVICES_ATTRIBUTION
ADDED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c42e3f53f251f72190ffef6945138b19\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:26:5-88
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2e44da5bd1abc324905da37f6753f3bb\transformed\play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:27:5-88
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2e44da5bd1abc324905da37f6753f3bb\transformed\play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:27:5-88
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c42e3f53f251f72190ffef6945138b19\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:26:22-85
uses-permission#android.permission.ACCESS_ADSERVICES_AD_ID
ADDED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c42e3f53f251f72190ffef6945138b19\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:27:5-82
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2e44da5bd1abc324905da37f6753f3bb\transformed\play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:28:5-82
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2e44da5bd1abc324905da37f6753f3bb\transformed\play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:28:5-82
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c42e3f53f251f72190ffef6945138b19\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:27:22-79
property
ADDED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c42e3f53f251f72190ffef6945138b19\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:30:9-32:61
	android:resource
		ADDED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c42e3f53f251f72190ffef6945138b19\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:32:13-58
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c42e3f53f251f72190ffef6945138b19\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:31:13-65
meta-data#com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar
ADDED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c42e3f53f251f72190ffef6945138b19\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:37:13-39:85
	android:value
		ADDED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c42e3f53f251f72190ffef6945138b19\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:39:17-82
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c42e3f53f251f72190ffef6945138b19\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:38:17-139
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar
ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4550ebb1f57cdacda2347951a8b355e7\transformed\firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4550ebb1f57cdacda2347951a8b355e7\transformed\firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4550ebb1f57cdacda2347951a8b355e7\transformed\firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar
ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4550ebb1f57cdacda2347951a8b355e7\transformed\firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4550ebb1f57cdacda2347951a8b355e7\transformed\firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4550ebb1f57cdacda2347951a8b355e7\transformed\firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
meta-data#com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar
ADDED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\ce87eaf2450b4f0f51a49634d42a1887\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\ce87eaf2450b4f0f51a49634d42a1887\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\ce87eaf2450b4f0f51a49634d42a1887\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:13:17-116
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\29a9afe92f618d0e0b754a9bfb76a586\transformed\firebase-common-20.4.2\AndroidManifest.xml:23:9-28:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\29a9afe92f618d0e0b754a9bfb76a586\transformed\firebase-common-20.4.2\AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\29a9afe92f618d0e0b754a9bfb76a586\transformed\firebase-common-20.4.2\AndroidManifest.xml:27:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\29a9afe92f618d0e0b754a9bfb76a586\transformed\firebase-common-20.4.2\AndroidManifest.xml:26:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\29a9afe92f618d0e0b754a9bfb76a586\transformed\firebase-common-20.4.2\AndroidManifest.xml:28:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\29a9afe92f618d0e0b754a9bfb76a586\transformed\firebase-common-20.4.2\AndroidManifest.xml:24:13-77
meta-data#com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar
ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\29a9afe92f618d0e0b754a9bfb76a586\transformed\firebase-common-20.4.2\AndroidManifest.xml:35:13-37:85
	android:value
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\29a9afe92f618d0e0b754a9bfb76a586\transformed\firebase-common-20.4.2\AndroidManifest.xml:37:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\29a9afe92f618d0e0b754a9bfb76a586\transformed\firebase-common-20.4.2\AndroidManifest.xml:36:17-109
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\39a8a2aca2bad19af9fb513e99abd016\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\39a8a2aca2bad19af9fb513e99abd016\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\39a8a2aca2bad19af9fb513e99abd016\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
service#androidx.work.impl.background.systemalarm.SystemAlarmService
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:39:9-45:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:42:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:43:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:44:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:45:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:41:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:40:13-88
service#androidx.work.impl.background.systemjob.SystemJobService
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:46:9-52:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:49:13-70
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:50:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:51:13-69
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:52:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:48:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:47:13-84
service#androidx.work.impl.foreground.SystemForegroundService
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:53:9-59:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:56:13-77
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:57:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:58:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:59:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:55:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:54:13-81
receiver#androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:61:9-66:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:64:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:65:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:66:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:63:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:62:13-88
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:67:9-77:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:70:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:71:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:72:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:69:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:68:13-106
intent-filter#action:name:android.intent.action.ACTION_POWER_CONNECTED+action:name:android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:73:13-76:29
action#android.intent.action.ACTION_POWER_CONNECTED
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:17-87
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:25-84
action#android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:17-90
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:25-87
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:78:9-88:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:81:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:82:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:83:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:80:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:79:13-104
intent-filter#action:name:android.intent.action.BATTERY_LOW+action:name:android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:84:13-87:29
action#android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:17-77
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:25-74
action#android.intent.action.BATTERY_LOW
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:17-76
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:25-73
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:89:9-99:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:92:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:93:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:94:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:91:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:90:13-104
intent-filter#action:name:android.intent.action.DEVICE_STORAGE_LOW+action:name:android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:95:13-98:29
action#android.intent.action.DEVICE_STORAGE_LOW
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:17-83
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:25-80
action#android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:17-82
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:25-79
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:100:9-109:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:103:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:104:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:105:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:102:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:101:13-103
intent-filter#action:name:android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:106:13-108:29
action#android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:25-76
receiver#androidx.work.impl.background.systemalarm.RescheduleReceiver
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:110:9-121:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:113:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:114:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:115:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:112:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:111:13-88
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.TIMEZONE_CHANGED+action:name:android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:116:13-120:29
action#android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:17-73
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:25-70
action#android.intent.action.TIMEZONE_CHANGED
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:17-81
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:25-78
receiver#androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:122:9-131:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:125:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:126:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:127:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:124:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:123:13-99
intent-filter#action:name:androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:128:13-130:29
action#androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:17-98
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:25-95
receiver#androidx.work.impl.diagnostics.DiagnosticsReceiver
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:132:9-142:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:135:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:136:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:137:13-57
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:138:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:134:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:133:13-78
intent-filter#action:name:androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:139:13-141:29
action#androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:17-88
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:25-85
uses-permission#com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE
ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4cce030851b64fce14a30f35636cafff\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\08cd672f2bf9942df1c4039c23a8420a\transformed\play-services-measurement-impl-21.5.0\AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\08cd672f2bf9942df1c4039c23a8420a\transformed\play-services-measurement-impl-21.5.0\AndroidManifest.xml:26:5-110
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4cce030851b64fce14a30f35636cafff\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:26:22-107
receiver#com.google.android.gms.measurement.AppMeasurementReceiver
ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4cce030851b64fce14a30f35636cafff\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:29:9-33:20
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4cce030851b64fce14a30f35636cafff\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:31:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4cce030851b64fce14a30f35636cafff\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:32:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4cce030851b64fce14a30f35636cafff\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:30:13-85
service#com.google.android.gms.measurement.AppMeasurementService
ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4cce030851b64fce14a30f35636cafff\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:35:9-38:40
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4cce030851b64fce14a30f35636cafff\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4cce030851b64fce14a30f35636cafff\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:38:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4cce030851b64fce14a30f35636cafff\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:36:13-84
service#com.google.android.gms.measurement.AppMeasurementJobService
ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4cce030851b64fce14a30f35636cafff\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:39:9-43:72
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4cce030851b64fce14a30f35636cafff\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:41:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4cce030851b64fce14a30f35636cafff\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:42:13-37
	android:permission
		ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4cce030851b64fce14a30f35636cafff\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:43:13-69
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4cce030851b64fce14a30f35636cafff\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:40:13-87
uses-library#android.ext.adservices
ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.12\transforms\135d01c71a961225103e48b7dd206008\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.12\transforms\135d01c71a961225103e48b7dd206008\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.12\transforms\135d01c71a961225103e48b7dd206008\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e661d63d9c04259b2495b443a4caf917\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e661d63d9c04259b2495b443a4caf917\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e661d63d9c04259b2495b443a4caf917\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e661d63d9c04259b2495b443a4caf917\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e661d63d9c04259b2495b443a4caf917\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e661d63d9c04259b2495b443a4caf917\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\29aa2070c5f46de89120ba6e20f2606d\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\29aa2070c5f46de89120ba6e20f2606d\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\29aa2070c5f46de89120ba6e20f2606d\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
permission#com.muslimcore.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\29aa2070c5f46de89120ba6e20f2606d\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\29aa2070c5f46de89120ba6e20f2606d\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\29aa2070c5f46de89120ba6e20f2606d\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\29aa2070c5f46de89120ba6e20f2606d\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\29aa2070c5f46de89120ba6e20f2606d\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
uses-permission#com.muslimcore.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\29aa2070c5f46de89120ba6e20f2606d\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\29aa2070c5f46de89120ba6e20f2606d\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\683da4f7ef5696e7dcd83bd164cc9897\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\683da4f7ef5696e7dcd83bd164cc9897\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\683da4f7ef5696e7dcd83bd164cc9897\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
meta-data#com.google.firebase.components:com.google.firebase.abt.component.AbtRegistrar
ADDED from [com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5565bd1e3d8bedbb48c3d9ebd009d2d6\transformed\firebase-abt-21.1.1\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5565bd1e3d8bedbb48c3d9ebd009d2d6\transformed\firebase-abt-21.1.1\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5565bd1e3d8bedbb48c3d9ebd009d2d6\transformed\firebase-abt-21.1.1\AndroidManifest.xml:13:17-109
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\812136ae31eafce15fafbc5dfed6f6ac\transformed\play-services-basement-18.1.0\AndroidManifest.xml:21:9-23:69
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\812136ae31eafce15fafbc5dfed6f6ac\transformed\play-services-basement-18.1.0\AndroidManifest.xml:23:13-66
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\812136ae31eafce15fafbc5dfed6f6ac\transformed\play-services-basement-18.1.0\AndroidManifest.xml:22:13-58
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\baa7f398d5d0d1385b12ae8bfc214adc\transformed\room-runtime-2.6.0\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime:2.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\baa7f398d5d0d1385b12ae8bfc214adc\transformed\room-runtime-2.6.0\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime:2.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\baa7f398d5d0d1385b12ae8bfc214adc\transformed\room-runtime-2.6.0\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\baa7f398d5d0d1385b12ae8bfc214adc\transformed\room-runtime-2.6.0\AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\baa7f398d5d0d1385b12ae8bfc214adc\transformed\room-runtime-2.6.0\AndroidManifest.xml:25:13-74
meta-data#com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar
ADDED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\3ca3bb862412792bd96083ec00cacda6\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:27:13-29:85
	android:value
		ADDED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\3ca3bb862412792bd96083ec00cacda6\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:29:17-82
	android:name
		ADDED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\3ca3bb862412792bd96083ec00cacda6\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:28:17-115
service#com.google.android.datatransport.runtime.backends.TransportBackendDiscovery
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.12\transforms\683f793d9b7aa5372f5619ebe6359687\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:28:9-34:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.12\transforms\4d198d6f53404638e45dff19d597f6a7\transformed\transport-runtime-3.1.8\AndroidManifest.xml:36:9-38:40
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.12\transforms\4d198d6f53404638e45dff19d597f6a7\transformed\transport-runtime-3.1.8\AndroidManifest.xml:36:9-38:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.12\transforms\683f793d9b7aa5372f5619ebe6359687\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.12\transforms\683f793d9b7aa5372f5619ebe6359687\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:29:13-103
meta-data#backend:com.google.android.datatransport.cct.CctBackendFactory
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.12\transforms\683f793d9b7aa5372f5619ebe6359687\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:31:13-33:39
	android:value
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.12\transforms\683f793d9b7aa5372f5619ebe6359687\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:33:17-36
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.12\transforms\683f793d9b7aa5372f5619ebe6359687\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:32:17-94
service#com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService
ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.12\transforms\4d198d6f53404638e45dff19d597f6a7\transformed\transport-runtime-3.1.8\AndroidManifest.xml:26:9-30:19
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.12\transforms\4d198d6f53404638e45dff19d597f6a7\transformed\transport-runtime-3.1.8\AndroidManifest.xml:28:13-37
	android:permission
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.12\transforms\4d198d6f53404638e45dff19d597f6a7\transformed\transport-runtime-3.1.8\AndroidManifest.xml:29:13-69
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.12\transforms\4d198d6f53404638e45dff19d597f6a7\transformed\transport-runtime-3.1.8\AndroidManifest.xml:27:13-117
receiver#com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver
ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.12\transforms\4d198d6f53404638e45dff19d597f6a7\transformed\transport-runtime-3.1.8\AndroidManifest.xml:32:9-34:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.12\transforms\4d198d6f53404638e45dff19d597f6a7\transformed\transport-runtime-3.1.8\AndroidManifest.xml:34:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.12\transforms\4d198d6f53404638e45dff19d597f6a7\transformed\transport-runtime-3.1.8\AndroidManifest.xml:33:13-132
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ce2fbb511b1dd1207b7023c44f05bb81\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ce2fbb511b1dd1207b7023c44f05bb81\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ce2fbb511b1dd1207b7023c44f05bb81\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ce2fbb511b1dd1207b7023c44f05bb81\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ce2fbb511b1dd1207b7023c44f05bb81\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ce2fbb511b1dd1207b7023c44f05bb81\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ce2fbb511b1dd1207b7023c44f05bb81\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ce2fbb511b1dd1207b7023c44f05bb81\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ce2fbb511b1dd1207b7023c44f05bb81\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ce2fbb511b1dd1207b7023c44f05bb81\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ce2fbb511b1dd1207b7023c44f05bb81\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ce2fbb511b1dd1207b7023c44f05bb81\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ce2fbb511b1dd1207b7023c44f05bb81\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ce2fbb511b1dd1207b7023c44f05bb81\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ce2fbb511b1dd1207b7023c44f05bb81\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ce2fbb511b1dd1207b7023c44f05bb81\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ce2fbb511b1dd1207b7023c44f05bb81\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ce2fbb511b1dd1207b7023c44f05bb81\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ce2fbb511b1dd1207b7023c44f05bb81\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ce2fbb511b1dd1207b7023c44f05bb81\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ce2fbb511b1dd1207b7023c44f05bb81\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
