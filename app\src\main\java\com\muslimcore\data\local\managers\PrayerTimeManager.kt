package com.muslimcore.data.local.managers

import android.content.Context
import android.content.SharedPreferences
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.math.*

@Singleton
class PrayerTimeManager @Inject constructor(
    private val context: Context
) {
    
    private val prefs: SharedPreferences = context.getSharedPreferences("prayer_prefs", Context.MODE_PRIVATE)
    
    companion object {
        private const val PREF_CALCULATION_METHOD = "calculation_method"
        private const val DEFAULT_METHOD = "MWL" // Muslim World League
    }
    
    data class PrayerTimes(
        val fajr: Calendar,
        val sunrise: Calendar,
        val dhuhr: Calendar,
        val asr: Calendar,
        val maghrib: Calendar,
        val isha: Calendar,
        val date: Calendar = Calendar.getInstance()
    )
    
    data class NextPrayer(
        val name: String,
        val time: Calendar,
        val timeUntil: Long // milliseconds until prayer
    )
    
    enum class Prayer {
        FAJR, SUNRISE, DHUHR, ASR, MAGHRIB, ISHA
    }
    
    /**
     * Calculate prayer times for given location and date
     */
    fun calculatePrayerTimes(
        latitude: Double,
        longitude: Double,
        date: Calendar = Calendar.getInstance()
    ): PrayerTimes {
        val method = getCalculationMethod()
        
        // Julian day calculation
        val julianDay = getJulianDay(date)
        
        // Sun declination and equation of time
        val sunDeclination = getSunDeclination(julianDay)
        val equationOfTime = getEquationOfTime(julianDay)
        
        // Calculate prayer times
        val fajrAngle = getFajrAngle(method)
        val ishaAngle = getIshaAngle(method)
        
        val fajrTime = calculatePrayerTime(latitude, sunDeclination, fajrAngle, equationOfTime, longitude, true)
        val sunriseTime = calculatePrayerTime(latitude, sunDeclination, -0.833, equationOfTime, longitude, true)
        val dhuhrTime = 12.0 - equationOfTime / 60.0 + longitude / 15.0
        val asrTime = calculateAsrTime(latitude, sunDeclination, equationOfTime, longitude)
        val maghribTime = calculatePrayerTime(latitude, sunDeclination, -0.833, equationOfTime, longitude, false)
        val ishaTime = calculatePrayerTime(latitude, sunDeclination, ishaAngle, equationOfTime, longitude, false)
        
        // Convert to Calendar objects
        return PrayerTimes(
            fajr = timeToCalendar(fajrTime, date),
            sunrise = timeToCalendar(sunriseTime, date),
            dhuhr = timeToCalendar(dhuhrTime, date),
            asr = timeToCalendar(asrTime, date),
            maghrib = timeToCalendar(maghribTime, date),
            isha = timeToCalendar(ishaTime, date),
            date = date.clone() as Calendar
        )
    }
    
    /**
     * Get next prayer information
     */
    fun getNextPrayer(prayerTimes: PrayerTimes): NextPrayer {
        val now = Calendar.getInstance()
        val prayers = listOf(
            "Fajr" to prayerTimes.fajr,
            "Sunrise" to prayerTimes.sunrise,
            "Dhuhr" to prayerTimes.dhuhr,
            "Asr" to prayerTimes.asr,
            "Maghrib" to prayerTimes.maghrib,
            "Isha" to prayerTimes.isha
        )
        
        // Find next prayer today
        for ((name, time) in prayers) {
            if (time.timeInMillis > now.timeInMillis) {
                return NextPrayer(
                    name = name,
                    time = time,
                    timeUntil = time.timeInMillis - now.timeInMillis
                )
            }
        }
        
        // If no prayer left today, get tomorrow's Fajr
        val tomorrow = Calendar.getInstance().apply {
            add(Calendar.DAY_OF_YEAR, 1)
        }
        
        // Get current location for tomorrow's calculation
        // This would need to be passed or retrieved from LocationManager
        val tomorrowPrayers = calculatePrayerTimes(0.0, 0.0, tomorrow) // Placeholder coordinates
        
        return NextPrayer(
            name = "Fajr",
            time = tomorrowPrayers.fajr,
            timeUntil = tomorrowPrayers.fajr.timeInMillis - now.timeInMillis
        )
    }
    
    /**
     * Format time until next prayer as HH:MM:SS
     */
    fun formatTimeUntil(milliseconds: Long): String {
        val totalSeconds = milliseconds / 1000
        val hours = totalSeconds / 3600
        val minutes = (totalSeconds % 3600) / 60
        val seconds = totalSeconds % 60
        
        return String.format("%02d:%02d:%02d", hours, minutes, seconds)
    }
    
    /**
     * Check if it's time for a specific prayer (within 1 minute)
     */
    fun isPrayerTime(prayerTimes: PrayerTimes): Prayer? {
        val now = Calendar.getInstance()
        val prayers = mapOf(
            Prayer.FAJR to prayerTimes.fajr,
            Prayer.DHUHR to prayerTimes.dhuhr,
            Prayer.ASR to prayerTimes.asr,
            Prayer.MAGHRIB to prayerTimes.maghrib,
            Prayer.ISHA to prayerTimes.isha
        )
        
        for ((prayer, time) in prayers) {
            val timeDiff = abs(now.timeInMillis - time.timeInMillis)
            if (timeDiff <= 60000) { // Within 1 minute
                return prayer
            }
        }
        
        return null
    }
    
    // Private calculation methods
    private fun getJulianDay(date: Calendar): Double {
        val year = date.get(Calendar.YEAR)
        val month = date.get(Calendar.MONTH) + 1
        val day = date.get(Calendar.DAY_OF_MONTH)
        
        val a = (14 - month) / 12
        val y = year - a
        val m = month + 12 * a - 3
        
        return day + (153 * m + 2) / 5 + 365 * y + y / 4 - y / 100 + y / 400 + 1721119
    }
    
    private fun getSunDeclination(julianDay: Double): Double {
        val n = julianDay - 2451545.0
        val l = (280.460 + 0.9856474 * n) % 360
        val g = Math.toRadians((357.528 + 0.9856003 * n) % 360)
        val lambda = Math.toRadians(l + 1.915 * sin(g) + 0.020 * sin(2 * g))
        
        return asin(sin(Math.toRadians(23.439)) * sin(lambda))
    }
    
    private fun getEquationOfTime(julianDay: Double): Double {
        val n = julianDay - 2451545.0
        val l = Math.toRadians((280.460 + 0.9856474 * n) % 360)
        val g = Math.toRadians((357.528 + 0.9856003 * n) % 360)
        val lambda = l + Math.toRadians(1.915 * sin(g) + 0.020 * sin(2 * g))
        val alpha = atan2(cos(Math.toRadians(23.439)) * sin(lambda), cos(lambda))
        
        return 4 * Math.toDegrees(l - alpha)
    }
    
    private fun calculatePrayerTime(
        latitude: Double,
        declination: Double,
        angle: Double,
        equationOfTime: Double,
        longitude: Double,
        isMorning: Boolean
    ): Double {
        val latRad = Math.toRadians(latitude)
        val angleRad = Math.toRadians(angle)
        
        val cosH = (sin(angleRad) - sin(latRad) * sin(declination)) / (cos(latRad) * cos(declination))
        
        if (cosH < -1 || cosH > 1) {
            return if (isMorning) 0.0 else 24.0 // Extreme latitudes
        }
        
        val h = Math.toDegrees(acos(cosH)) / 15.0
        
        return if (isMorning) {
            12.0 - h - equationOfTime / 60.0 + longitude / 15.0
        } else {
            12.0 + h - equationOfTime / 60.0 + longitude / 15.0
        }
    }
    
    private fun calculateAsrTime(
        latitude: Double,
        declination: Double,
        equationOfTime: Double,
        longitude: Double
    ): Double {
        val latRad = Math.toRadians(latitude)
        val factor = 1 + tan(abs(latRad - declination))
        val angle = -Math.toDegrees(atan(1.0 / factor))
        
        return calculatePrayerTime(latitude, declination, angle, equationOfTime, longitude, false)
    }
    
    private fun timeToCalendar(time: Double, date: Calendar): Calendar {
        val calendar = date.clone() as Calendar
        val hours = time.toInt()
        val minutes = ((time - hours) * 60).toInt()
        val seconds = (((time - hours) * 60 - minutes) * 60).toInt()
        
        calendar.set(Calendar.HOUR_OF_DAY, hours)
        calendar.set(Calendar.MINUTE, minutes)
        calendar.set(Calendar.SECOND, seconds)
        calendar.set(Calendar.MILLISECOND, 0)
        
        return calendar
    }
    
    private fun getFajrAngle(method: String): Double {
        return when (method) {
            "MWL" -> -18.0 // Muslim World League
            "ISNA" -> -15.0 // Islamic Society of North America
            "Egypt" -> -19.5 // Egyptian General Authority of Survey
            "Makkah" -> -18.5 // Umm Al-Qura University, Makkah
            "Karachi" -> -18.0 // University of Islamic Sciences, Karachi
            else -> -18.0
        }
    }
    
    private fun getIshaAngle(method: String): Double {
        return when (method) {
            "MWL" -> -17.0 // Muslim World League
            "ISNA" -> -15.0 // Islamic Society of North America
            "Egypt" -> -17.5 // Egyptian General Authority of Survey
            "Makkah" -> -18.5 // Umm Al-Qura University, Makkah
            "Karachi" -> -18.0 // University of Islamic Sciences, Karachi
            else -> -17.0
        }
    }
    
    private fun getCalculationMethod(): String {
        return prefs.getString(PREF_CALCULATION_METHOD, DEFAULT_METHOD) ?: DEFAULT_METHOD
    }
    
    fun setCalculationMethod(method: String) {
        prefs.edit().putString(PREF_CALCULATION_METHOD, method).apply()
    }
}
