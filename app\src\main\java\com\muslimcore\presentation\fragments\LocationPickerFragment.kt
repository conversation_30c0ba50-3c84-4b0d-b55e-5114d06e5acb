package com.muslimcore.presentation.fragments

import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.muslimcore.data.local.managers.LocationManager
import com.muslimcore.databinding.FragmentLocationPickerBinding
import com.muslimcore.presentation.adapters.LocationAdapter
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import javax.inject.Inject

@AndroidEntryPoint
class LocationPickerFragment : Fragment() {

    private var _binding: FragmentLocationPickerBinding? = null
    private val binding get() = _binding!!
    
    @Inject
    lateinit var locationManager: LocationManager
    
    private lateinit var locationAdapter: LocationAdapter
    private var currentLocation: LocationManager.LocationData? = null
    private var selectedLocation: LocationManager.LocationData? = null
    private var searchJob: Job? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentLocationPickerBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        setupRecyclerView()
        setupClickListeners()
        setupSearch()
        loadCurrentLocation()
        loadPopularCities()
    }

    private fun setupRecyclerView() {
        locationAdapter = LocationAdapter { location ->
            selectedLocation = location
            // Save selected location and navigate back
            saveSelectedLocation(location)
        }
        
        binding.recyclerCities.apply {
            layoutManager = LinearLayoutManager(requireContext())
            adapter = locationAdapter
        }
    }

    private fun setupClickListeners() {
        binding.buttonBack.setOnClickListener {
            requireActivity().onBackPressed()
        }

        binding.buttonUseCurrent.setOnClickListener {
            currentLocation?.let { location ->
                selectedLocation = location
                saveSelectedLocation(location)
            }
        }
    }

    private fun setupSearch() {
        binding.editSearch.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
            
            override fun afterTextChanged(s: Editable?) {
                val query = s?.toString()?.trim()
                if (query.isNullOrEmpty()) {
                    loadPopularCities()
                } else {
                    searchLocations(query)
                }
            }
        })
    }

    private fun loadCurrentLocation() {
        lifecycleScope.launch {
            try {
                binding.textCurrentLocation.text = "Detecting..."
                currentLocation = locationManager.getCurrentLocation()
                
                currentLocation?.let { location ->
                    binding.textCurrentLocation.text = "${location.cityName}, ${location.countryName}"
                    binding.buttonUseCurrent.isEnabled = true
                } ?: run {
                    binding.textCurrentLocation.text = "Unable to detect location"
                    binding.buttonUseCurrent.isEnabled = false
                }
            } catch (e: Exception) {
                binding.textCurrentLocation.text = "Location error"
                binding.buttonUseCurrent.isEnabled = false
            }
        }
    }

    private fun loadPopularCities() {
        val popularCities = locationManager.getPopularIslamicCities()
        locationAdapter.submitList(popularCities)
    }

    private fun searchLocations(query: String) {
        // Cancel previous search
        searchJob?.cancel()
        
        searchJob = lifecycleScope.launch {
            try {
                // Add delay to avoid too many API calls
                delay(500)
                
                val searchResults = locationManager.searchLocations(query)
                if (searchResults.isNotEmpty()) {
                    locationAdapter.submitList(searchResults)
                } else {
                    // Show popular cities if no results
                    loadPopularCities()
                }
            } catch (e: Exception) {
                // Show popular cities on error
                loadPopularCities()
            }
        }
    }
    
    private fun saveSelectedLocation(location: LocationManager.LocationData) {
        // Save the selected location
        locationManager.setLocation(location)

        // Navigate back to prayer fragment
        requireActivity().onBackPressed()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        searchJob?.cancel()
        _binding = null
    }
}
