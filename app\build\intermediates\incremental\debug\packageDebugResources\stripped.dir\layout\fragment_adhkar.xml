<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#1A1A1A"
    android:orientation="vertical">

    <!-- Header with Back Button and Title -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="#1A1A1A"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:padding="16dp">

        <ImageButton
            android:id="@+id/button_back"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_arrow_left"
            app:tint="@android:color/white" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_weight="1"
            android:text="Duas"
            android:textColor="@android:color/white"
            android:textSize="20sp"
            android:textStyle="bold" />

        <ImageButton
            android:id="@+id/button_translate"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_translate"
            app:tint="@android:color/white" />

    </LinearLayout>

    <!-- Tab Layout -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="16dp"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tab_categories"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_weight="1"
            android:background="@drawable/tab_selected_background"
            android:gravity="center"
            android:text="Categories"
            android:textColor="#0E7C4B"
            android:textSize="16sp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tab_my_duas"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_weight="1"
            android:gravity="center"
            android:text="My Duas"
            android:textColor="#666666"
            android:textSize="16sp" />

    </LinearLayout>

    <!-- Search Bar -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        android:background="@drawable/search_background"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:padding="12dp">

        <ImageView
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_marginEnd="12dp"
            android:src="@drawable/ic_search"
            app:tint="#666666" />

        <EditText
            android:id="@+id/edit_search"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:background="@null"
            android:hint="Search"
            android:textColor="@android:color/white"
            android:textColorHint="#666666"
            android:textSize="16sp" />

    </LinearLayout>

    <!-- Content ScrollView -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Title -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:text="Hisnul Muslim (Fortress of the Muslim)"
                android:textColor="@android:color/white"
                android:textSize="16sp"
                android:textStyle="bold" />

            <!-- Categories Grid -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <!-- Row 1 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="12dp"
                    android:orientation="horizontal">

                    <!-- All Card -->
                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/card_all"
                        android:layout_width="0dp"
                        android:layout_height="120dp"
                        android:layout_marginEnd="6dp"
                        android:layout_weight="1"
                        app:cardBackgroundColor="#2A2A2A"
                        app:cardCornerRadius="12dp"
                        app:cardElevation="0dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:gravity="center"
                            android:orientation="vertical"
                            android:padding="16dp">

                            <ImageView
                                android:layout_width="40dp"
                                android:layout_height="40dp"
                                android:layout_marginBottom="8dp"
                                android:src="@drawable/ic_book"
                                app:tint="#FFA726" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginBottom="4dp"
                                android:text="All"
                                android:textColor="@android:color/white"
                                android:textSize="14sp"
                                android:textStyle="bold" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="132 Chapters"
                                android:textColor="#999999"
                                android:textSize="12sp" />

                        </LinearLayout>

                    </com.google.android.material.card.MaterialCardView>

                    <!-- Morning & Evening Card -->
                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/card_morning_evening"
                        android:layout_width="0dp"
                        android:layout_height="120dp"
                        android:layout_marginStart="6dp"
                        android:layout_weight="1"
                        app:cardBackgroundColor="#2A2A2A"
                        app:cardCornerRadius="12dp"
                        app:cardElevation="0dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:gravity="center"
                            android:orientation="vertical"
                            android:padding="16dp">

                            <ImageView
                                android:layout_width="40dp"
                                android:layout_height="40dp"
                                android:layout_marginBottom="8dp"
                                android:src="@drawable/ic_sunrise"
                                app:tint="#42A5F5" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginBottom="4dp"
                                android:text="Morning &amp; Evening"
                                android:textColor="@android:color/white"
                                android:textSize="14sp"
                                android:textStyle="bold" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="6 Chapters"
                                android:textColor="#999999"
                                android:textSize="12sp" />

                        </LinearLayout>

                    </com.google.android.material.card.MaterialCardView>

                </LinearLayout>

                <!-- Row 2 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="12dp"
                    android:orientation="horizontal">

                    <!-- Prayer Card -->
                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/card_prayer"
                        android:layout_width="0dp"
                        android:layout_height="120dp"
                        android:layout_marginEnd="6dp"
                        android:layout_weight="1"
                        app:cardBackgroundColor="#2A2A2A"
                        app:cardCornerRadius="12dp"
                        app:cardElevation="0dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:gravity="center"
                            android:orientation="vertical"
                            android:padding="16dp">

                            <ImageView
                                android:layout_width="40dp"
                                android:layout_height="40dp"
                                android:layout_marginBottom="8dp"
                                android:src="@drawable/ic_prayer"
                                app:tint="#66BB6A" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginBottom="4dp"
                                android:text="Prayer"
                                android:textColor="@android:color/white"
                                android:textSize="14sp"
                                android:textStyle="bold" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="19 Chapters"
                                android:textColor="#999999"
                                android:textSize="12sp" />

                        </LinearLayout>

                    </com.google.android.material.card.MaterialCardView>

                    <!-- Praising Allah Card -->
                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/card_praising_allah"
                        android:layout_width="0dp"
                        android:layout_height="120dp"
                        android:layout_marginStart="6dp"
                        android:layout_weight="1"
                        app:cardBackgroundColor="#2A2A2A"
                        app:cardCornerRadius="12dp"
                        app:cardElevation="0dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:gravity="center"
                            android:orientation="vertical"
                            android:padding="16dp">

                            <ImageView
                                android:layout_width="40dp"
                                android:layout_height="40dp"
                                android:layout_marginBottom="8dp"
                                android:src="@drawable/ic_adhkar"
                                app:tint="#AB47BC" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginBottom="4dp"
                                android:text="Praising Allah"
                                android:textColor="@android:color/white"
                                android:textSize="14sp"
                                android:textStyle="bold" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="9 Chapters"
                                android:textColor="#999999"
                                android:textSize="12sp" />

                        </LinearLayout>

                    </com.google.android.material.card.MaterialCardView>

                </LinearLayout>

                <!-- Row 3 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="12dp"
                    android:orientation="horizontal">

                    <!-- Hajj & Umrah Card -->
                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/card_hajj_umrah"
                        android:layout_width="0dp"
                        android:layout_height="120dp"
                        android:layout_marginEnd="6dp"
                        android:layout_weight="1"
                        app:cardBackgroundColor="#2A2A2A"
                        app:cardCornerRadius="12dp"
                        app:cardElevation="0dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:gravity="center"
                            android:orientation="vertical"
                            android:padding="16dp">

                            <ImageView
                                android:layout_width="40dp"
                                android:layout_height="40dp"
                                android:layout_marginBottom="8dp"
                                android:src="@drawable/ic_location"
                                app:tint="#8D6E63" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginBottom="4dp"
                                android:text="Hajj &amp; Umrah"
                                android:textColor="@android:color/white"
                                android:textSize="14sp"
                                android:textStyle="bold" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="8 Chapters"
                                android:textColor="#999999"
                                android:textSize="12sp" />

                        </LinearLayout>

                    </com.google.android.material.card.MaterialCardView>

                    <!-- Travel Card -->
                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/card_travel"
                        android:layout_width="0dp"
                        android:layout_height="120dp"
                        android:layout_marginStart="6dp"
                        android:layout_weight="1"
                        app:cardBackgroundColor="#2A2A2A"
                        app:cardCornerRadius="12dp"
                        app:cardElevation="0dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:gravity="center"
                            android:orientation="vertical"
                            android:padding="16dp">

                            <ImageView
                                android:layout_width="40dp"
                                android:layout_height="40dp"
                                android:layout_marginBottom="8dp"
                                android:src="@drawable/ic_arrow_forward"
                                app:tint="#FFB74D" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginBottom="4dp"
                                android:text="Travel"
                                android:textColor="@android:color/white"
                                android:textSize="14sp"
                                android:textStyle="bold" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="11 Chapters"
                                android:textColor="#999999"
                                android:textSize="12sp" />

                        </LinearLayout>

                    </com.google.android.material.card.MaterialCardView>

                </LinearLayout>

                <!-- Row 4 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="12dp"
                    android:orientation="horizontal">

                    <!-- Joy & Distress Card -->
                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/card_joy_distress"
                        android:layout_width="0dp"
                        android:layout_height="120dp"
                        android:layout_marginEnd="6dp"
                        android:layout_weight="1"
                        app:cardBackgroundColor="#2A2A2A"
                        app:cardCornerRadius="12dp"
                        app:cardElevation="0dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:gravity="center"
                            android:orientation="vertical"
                            android:padding="16dp">

                            <ImageView
                                android:layout_width="40dp"
                                android:layout_height="40dp"
                                android:layout_marginBottom="8dp"
                                android:src="@drawable/ic_circle"
                                app:tint="#FFEB3B" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginBottom="4dp"
                                android:text="Joy &amp; Distress"
                                android:textColor="@android:color/white"
                                android:textSize="14sp"
                                android:textStyle="bold" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="15 Chapters"
                                android:textColor="#999999"
                                android:textSize="12sp" />

                        </LinearLayout>

                    </com.google.android.material.card.MaterialCardView>

                    <!-- Nature Card -->
                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/card_nature"
                        android:layout_width="0dp"
                        android:layout_height="120dp"
                        android:layout_marginStart="6dp"
                        android:layout_weight="1"
                        app:cardBackgroundColor="#2A2A2A"
                        app:cardCornerRadius="12dp"
                        app:cardElevation="0dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:gravity="center"
                            android:orientation="vertical"
                            android:padding="16dp">

                            <ImageView
                                android:layout_width="40dp"
                                android:layout_height="40dp"
                                android:layout_marginBottom="8dp"
                                android:src="@drawable/ic_circle"
                                app:tint="#4CAF50" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginBottom="4dp"
                                android:text="Nature"
                                android:textColor="@android:color/white"
                                android:textSize="14sp"
                                android:textStyle="bold" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="10 Chapters"
                                android:textColor="#999999"
                                android:textSize="12sp" />

                        </LinearLayout>

                    </com.google.android.material.card.MaterialCardView>

                </LinearLayout>

                <!-- Row 5 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <!-- Good Etiquette Card -->
                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/card_good_etiquette"
                        android:layout_width="0dp"
                        android:layout_height="120dp"
                        android:layout_marginEnd="6dp"
                        android:layout_weight="1"
                        app:cardBackgroundColor="#2A2A2A"
                        app:cardCornerRadius="12dp"
                        app:cardElevation="0dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:gravity="center"
                            android:orientation="vertical"
                            android:padding="16dp">

                            <ImageView
                                android:layout_width="40dp"
                                android:layout_height="40dp"
                                android:layout_marginBottom="8dp"
                                android:src="@drawable/ic_check"
                                app:tint="#FFC107" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginBottom="4dp"
                                android:text="Good Etiquette"
                                android:textColor="@android:color/white"
                                android:textSize="14sp"
                                android:textStyle="bold" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="20 Chapters"
                                android:textColor="#999999"
                                android:textSize="12sp" />

                        </LinearLayout>

                    </com.google.android.material.card.MaterialCardView>

                    <!-- Home & Family Card -->
                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/card_home_family"
                        android:layout_width="0dp"
                        android:layout_height="120dp"
                        android:layout_marginStart="6dp"
                        android:layout_weight="1"
                        app:cardBackgroundColor="#2A2A2A"
                        app:cardCornerRadius="12dp"
                        app:cardElevation="0dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:gravity="center"
                            android:orientation="vertical"
                            android:padding="16dp">

                            <ImageView
                                android:layout_width="40dp"
                                android:layout_height="40dp"
                                android:layout_marginBottom="8dp"
                                android:src="@drawable/ic_circle"
                                app:tint="#2196F3" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginBottom="4dp"
                                android:text="Home &amp; Family"
                                android:textColor="@android:color/white"
                                android:textSize="14sp"
                                android:textStyle="bold" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="14 Chapters"
                                android:textColor="#999999"
                                android:textSize="12sp" />

                        </LinearLayout>

                    </com.google.android.material.card.MaterialCardView>

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

    </ScrollView>

</LinearLayout>
