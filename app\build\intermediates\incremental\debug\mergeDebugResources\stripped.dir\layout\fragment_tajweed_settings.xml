<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_primary"
    android:fillViewport="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Header -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="24dp"
            android:fontFamily="sans-serif-medium"
            android:gravity="center"
            android:text="Tajweed Settings"
            android:textColor="@color/text_primary"
            android:textSize="24sp" />

        <!-- <PERSON><PERSON><PERSON> Toggle Card -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="24dp"
            app:cardBackgroundColor="@color/background_secondary"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:fontFamily="sans-serif-medium"
                            android:text="Enable Tajweed Coloring"
                            android:textColor="@color/text_primary"
                            android:textSize="18sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:fontFamily="sans-serif"
                            android:text="Color-code Quran text according to tajweed rules"
                            android:textColor="@color/text_secondary"
                            android:textSize="14sp" />

                    </LinearLayout>

                    <Switch
                        android:id="@+id/switch_tajweed_enabled"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:checked="true"
                        android:thumbTint="@color/islamic_green_primary"
                        android:trackTint="@color/text_secondary" />

                </LinearLayout>

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- Tajweed Legend Header -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:fontFamily="sans-serif-medium"
            android:text="Tajweed Color Legend"
            android:textColor="@color/text_primary"
            android:textSize="20sp" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:fontFamily="sans-serif"
            android:text="Learn what each color represents in Quranic recitation:"
            android:textColor="@color/text_secondary"
            android:textSize="14sp" />

        <!-- Tajweed Legend RecyclerView -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycler_view_tajweed_legend"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:nestedScrollingEnabled="false"
            tools:itemCount="6"
            tools:listitem="@layout/item_tajweed_legend" />

        <!-- Professional Note -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            app:cardBackgroundColor="@color/islamic_green_light"
            app:cardCornerRadius="12dp"
            app:cardElevation="2dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="sans-serif-medium"
                    android:text="📖 Professional Tajweed"
                    android:textColor="@color/islamic_green_primary"
                    android:textSize="16sp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:fontFamily="sans-serif"
                    android:text="This tajweed implementation follows the same standards used by Quran.com and traditional Islamic scholarship. Colors help you learn proper pronunciation and recitation rules."
                    android:textColor="@color/islamic_green_primary"
                    android:textSize="14sp" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

    </LinearLayout>

</ScrollView>
