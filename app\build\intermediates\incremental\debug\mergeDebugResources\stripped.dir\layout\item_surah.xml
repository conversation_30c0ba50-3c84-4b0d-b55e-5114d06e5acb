<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="16dp"
    android:layout_marginVertical="6dp"
    app:cardBackgroundColor="#1A5A4A"
    app:cardCornerRadius="12dp"
    app:cardElevation="2dp">

<LinearLayout
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="?android:attr/selectableItemBackground"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:paddingHorizontal="20dp"
    android:paddingVertical="24dp">

    <!-- Surah Number -->
    <TextView
        android:id="@+id/textSurahNumber"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:background="@drawable/circle_background"
        android:gravity="center"
        android:text="1"
        android:textColor="@android:color/white"
        android:textSize="18sp"
        android:textStyle="bold" />

    <!-- Surah Info -->
    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="20dp"
        android:layout_weight="1"
        android:orientation="vertical">

        <TextView
            android:id="@+id/textSurahEnglishName"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Al-Faatiha"
            android:textColor="@android:color/white"
            android:textSize="18sp"
            android:textStyle="bold"
            android:layout_marginBottom="4dp" />

        <TextView
            android:id="@+id/textSurahTranslation"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="The Opener"
            android:textColor="#80FFFFFF"
            android:textSize="15sp" />

    </LinearLayout>

    <!-- Arabic Name and Verse Count -->
    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="end"
        android:orientation="vertical">

        <TextView
            android:id="@+id/textSurahArabicName"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="الفاتحة"
            android:textColor="@android:color/white"
            android:textSize="28sp"
            android:textStyle="bold"
            android:layout_marginBottom="6dp"
            android:fontFamily="serif" />

        <TextView
            android:id="@+id/textVerseCount"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="7 Verses"
            android:textColor="#80FFFFFF"
            android:textSize="14sp"
            android:gravity="end" />

    </LinearLayout>

</LinearLayout>

</androidx.cardview.widget.CardView>
