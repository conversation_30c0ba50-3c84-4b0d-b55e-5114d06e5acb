<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:gravity="center_vertical"
    android:padding="12dp"
    android:background="?attr/selectableItemBackground"
    android:clickable="true"
    android:focusable="true">

    <ImageView
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:src="@drawable/ic_location"
        app:tint="#80FFFFFF"
        android:layout_marginEnd="12dp" />

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical">

        <TextView
            android:id="@+id/text_city_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Mecca"
            android:textColor="#FFFFFF"
            android:textSize="16sp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/text_country_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Saudi Arabia"
            android:textColor="#B0B0B0"
            android:textSize="12sp" />

    </LinearLayout>

    <ImageView
        android:id="@+id/icon_selected"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:src="@drawable/ic_check"
        app:tint="#4CAF50"
        android:visibility="gone" />

</LinearLayout>
