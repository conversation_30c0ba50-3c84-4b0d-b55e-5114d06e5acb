<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#2A2A2A"
    android:orientation="vertical">

    <!-- Header -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:padding="16dp"
        android:background="#1A1A1A">

        <!-- Back Button -->
        <ImageButton
            android:id="@+id/button_back"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_arrow_back"
            app:tint="#FFFFFF" />

        <!-- Title -->
        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="16dp"
            android:text="Tajweed Rules"
            android:textColor="#FFFFFF"
            android:textSize="20sp"
            android:textStyle="bold" />

    </LinearLayout>

    <!-- Content -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:padding="16dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- Tajweed Rules Content -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Tajweed Rules"
                android:textColor="#FFFFFF"
                android:textSize="24sp"
                android:textStyle="bold"
                android:layout_marginBottom="16dp" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Tajweed is the art of reciting the Quran correctly with proper pronunciation, rhythm, and intonation."
                android:textColor="#B0B0B0"
                android:textSize="16sp"
                android:lineSpacingMultiplier="1.4"
                android:layout_marginBottom="24dp" />

            <!-- Rule 1 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="16dp">

                <View
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:layout_marginEnd="12dp"
                    android:layout_marginTop="2dp"
                    android:background="#FF6B9D" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Ghunnah (Nasal Sound)"
                        android:textColor="#FFFFFF"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:layout_marginBottom="4dp" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="A nasal sound produced when pronouncing certain letters like Noon and Meem."
                        android:textColor="#B0B0B0"
                        android:textSize="14sp"
                        android:lineSpacingMultiplier="1.3" />

                </LinearLayout>

            </LinearLayout>

            <!-- Rule 2 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="16dp">

                <View
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:layout_marginEnd="12dp"
                    android:layout_marginTop="2dp"
                    android:background="#4CAF50" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Qalqalah (Echo Sound)"
                        android:textColor="#FFFFFF"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:layout_marginBottom="4dp" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="An echoing sound produced when pronouncing letters ق ط ب ج د when they have sukoon."
                        android:textColor="#B0B0B0"
                        android:textSize="14sp"
                        android:lineSpacingMultiplier="1.3" />

                </LinearLayout>

            </LinearLayout>

            <!-- Rule 3 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="16dp">

                <View
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:layout_marginEnd="12dp"
                    android:layout_marginTop="2dp"
                    android:background="#2196F3" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Madd (Prolongation)"
                        android:textColor="#FFFFFF"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:layout_marginBottom="4dp" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Lengthening the sound of certain vowels (Alif, Waw, Ya) for specific durations."
                        android:textColor="#B0B0B0"
                        android:textSize="14sp"
                        android:lineSpacingMultiplier="1.3" />

                </LinearLayout>

            </LinearLayout>

            <!-- Rule 4 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="16dp">

                <View
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:layout_marginEnd="12dp"
                    android:layout_marginTop="2dp"
                    android:background="#FF9800" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Idgham (Assimilation)"
                        android:textColor="#FFFFFF"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:layout_marginBottom="4dp" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Merging the sound of one letter into another when specific conditions are met."
                        android:textColor="#B0B0B0"
                        android:textSize="14sp"
                        android:lineSpacingMultiplier="1.3" />

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

    </ScrollView>

</LinearLayout>
