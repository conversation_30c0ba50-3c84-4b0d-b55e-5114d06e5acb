<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <!-- Semi-transparent Background (25% opacity) -->
    <View
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#40000000" />

    <!-- Settings Container (1/3 of screen from top) -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="top"
        android:maxHeight="400dp"
        android:orientation="vertical"
        android:padding="24dp"
        android:background="@drawable/blur_background"
        android:layout_marginTop="0dp">

        <!-- Theme Section -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Theme"
            android:textColor="#FFFFFF"
            android:textSize="18sp"
            android:textStyle="bold"
            android:layout_marginBottom="16dp" />

        <!-- Theme Buttons Row 1 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <Button
                android:id="@+id/btn_theme_current_dark"
                android:layout_width="0dp"
                android:layout_height="40dp"
                android:layout_weight="1"
                android:layout_marginEnd="8dp"
                android:text="Current (Dark)"
                android:textColor="#FFFFFF"
                android:textSize="11sp"
                android:background="@drawable/theme_button_selector" />

            <Button
                android:id="@+id/btn_theme_sepia"
                android:layout_width="0dp"
                android:layout_height="40dp"
                android:layout_weight="1"
                android:layout_marginEnd="8dp"
                android:text="Sepia"
                android:textColor="#FFFFFF"
                android:textSize="12sp"
                android:background="@drawable/theme_button_selector" />

            <Button
                android:id="@+id/btn_theme_light"
                android:layout_width="0dp"
                android:layout_height="40dp"
                android:layout_weight="1"
                android:text="Light"
                android:textColor="#FFFFFF"
                android:textSize="12sp"
                android:background="@drawable/theme_button_selector" />

        </LinearLayout>

        <!-- Theme Buttons Row 2 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="32dp">

            <Button
                android:id="@+id/btn_theme_black"
                android:layout_width="0dp"
                android:layout_height="40dp"
                android:layout_weight="1"
                android:layout_marginEnd="8dp"
                android:text="Black"
                android:textColor="#FFFFFF"
                android:textSize="12sp"
                android:background="@drawable/theme_button_selector" />

            <Button
                android:id="@+id/btn_theme_dark_blue"
                android:layout_width="0dp"
                android:layout_height="40dp"
                android:layout_weight="1"
                android:layout_marginEnd="8dp"
                android:text="Dark Blue"
                android:textColor="#FFFFFF"
                android:textSize="11sp"
                android:background="@drawable/theme_button_selector" />

            <!-- Empty space for alignment -->
            <View
                android:layout_width="0dp"
                android:layout_height="40dp"
                android:layout_weight="1" />

        </LinearLayout>

        <!-- Font & Size Section -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Font &amp; Size"
            android:textColor="#FFFFFF"
            android:textSize="18sp"
            android:textStyle="bold"
            android:layout_marginBottom="16dp" />

        <!-- Font Type Buttons -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="16dp">

            <Button
                android:id="@+id/btn_font_uthmani"
                android:layout_width="0dp"
                android:layout_height="40dp"
                android:layout_weight="1"
                android:layout_marginEnd="8dp"
                android:text="Uthmani"
                android:textColor="#FFFFFF"
                android:textSize="14sp"
                android:background="@drawable/font_button_selector" />

            <Button
                android:id="@+id/btn_font_indopak"
                android:layout_width="0dp"
                android:layout_height="40dp"
                android:layout_weight="1"
                android:text="IndoPak"
                android:textColor="#FFFFFF"
                android:textSize="14sp"
                android:background="@drawable/font_button_selector" />

        </LinearLayout>

        <!-- Size Slider and Bold Button -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="32dp">

            <SeekBar
                android:id="@+id/seekbar_text_size"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:max="20"
                android:progress="8"
                android:layout_marginEnd="16dp" />

            <ImageButton
                android:id="@+id/btn_bold"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:src="@drawable/ic_bold"
                android:background="@drawable/bold_button_selector"
                android:scaleType="centerInside" />

        </LinearLayout>

        <!-- Tajweed Section -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="32dp">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Tajweed"
                android:textColor="#FFFFFF"
                android:textSize="18sp"
                android:textStyle="bold" />

            <Switch
                android:id="@+id/switch_tajweed"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="8dp"
                android:thumbTint="#4CAF50"
                android:trackTint="#80FFFFFF" />

            <ImageButton
                android:id="@+id/btn_tajweed_info"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:src="@drawable/ic_info"
                android:background="@drawable/info_button_selector"
                android:scaleType="centerInside" />

        </LinearLayout>

        <!-- Action Buttons -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center"
            android:layout_marginTop="16dp">

            <Button
                android:id="@+id/button_cancel"
                android:layout_width="100dp"
                android:layout_height="40dp"
                android:layout_marginEnd="16dp"
                android:text="Cancel"
                android:textColor="#FFFFFF"
                android:textSize="14sp"
                android:background="@drawable/cancel_button_selector" />

            <Button
                android:id="@+id/button_done"
                android:layout_width="100dp"
                android:layout_height="40dp"
                android:text="Done"
                android:textColor="#FFFFFF"
                android:textSize="14sp"
                android:background="@drawable/done_button_selector" />

        </LinearLayout>

    </LinearLayout>

</FrameLayout>
