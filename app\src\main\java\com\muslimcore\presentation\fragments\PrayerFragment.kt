package com.muslimcore.presentation.fragments

import android.Manifest
import android.content.pm.PackageManager
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.muslimcore.data.local.managers.LocationManager
import com.muslimcore.data.local.managers.PrayerTimeManager
import com.muslimcore.databinding.FragmentPrayerBinding
import com.muslimcore.domain.entities.PrayerTimes
import com.muslimcore.presentation.MainActivity
import com.muslimcore.presentation.adapters.PrayerTimesAdapter

import com.muslimcore.presentation.utils.ThemeManager
import com.muslimcore.presentation.viewmodels.PrayerViewModel
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.*
import javax.inject.Inject

@AndroidEntryPoint
class PrayerFragment : Fragment() {

    private var _binding: FragmentPrayerBinding? = null
    private val binding get() = _binding!!

    private val viewModel: PrayerViewModel by viewModels()
    private lateinit var prayerTimesAdapter: PrayerTimesAdapter

    @Inject
    lateinit var locationManager: LocationManager

    @Inject
    lateinit var prayerTimeManager: PrayerTimeManager

    @Inject
    lateinit var themeManager: ThemeManager

    private var currentLocation: LocationManager.LocationData? = null
    private var currentPrayerTimes: PrayerTimeManager.PrayerTimes? = null
    private var countdownHandler: Handler? = null
    private var countdownRunnable: Runnable? = null

    // Location permission launcher
    private val locationPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestMultiplePermissions()
    ) { permissions ->
        val fineLocationGranted = permissions[Manifest.permission.ACCESS_FINE_LOCATION] ?: false
        val coarseLocationGranted = permissions[Manifest.permission.ACCESS_COARSE_LOCATION] ?: false

        if (fineLocationGranted || coarseLocationGranted) {
            loadCurrentLocation()
        } else {
            // Show default location or ask user to select manually
            binding.buttonLocation.text = "Select Location"
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentPrayerBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        try {
            setupRecyclerView()
            setupClickListeners()
            observeViewModel()

            // Initialize location and prayer times
            initializeLocationAndPrayerTimes()

            // Start countdown timer
            startCountdownTimer()

            // Apply theme
            val themeColors = themeManager.getThemeColors(requireContext())
            onThemeApplied(themeColors)
        } catch (e: Exception) {
            // Handle initialization error gracefully
            binding.buttonLocation.text = "Error loading"
            android.util.Log.e("PrayerFragment", "Error in onViewCreated", e)
        }
    }

    private fun setupRecyclerView() {
        prayerTimesAdapter = PrayerTimesAdapter { prayer, isCompleted ->
            viewModel.logPrayer(prayer, isCompleted)
        }

        binding.recyclerViewPrayerTimes.apply {
            layoutManager = LinearLayoutManager(requireContext())
            adapter = prayerTimesAdapter
            setHasFixedSize(true)
        }
    }

    private fun setupClickListeners() {
        binding.buttonLocation.setOnClickListener {
            navigateToLocationPicker()
        }

        binding.buttonNotificationSettings.setOnClickListener {
            navigateToNotificationSettings()
        }

        binding.buttonTrackPrayers.setOnClickListener {
            // Navigate to prayer tracking screen
            viewModel.navigateToTracker()
        }

        binding.buttonPreviousDay.setOnClickListener {
            // Navigate to previous day
            viewModel.loadPreviousDay()
        }

        binding.buttonNextDay.setOnClickListener {
            // Navigate to next day
            viewModel.loadNextDay()
        }
    }

    private fun observeViewModel() {
        viewLifecycleOwner.lifecycleScope.launch {
            viewModel.uiState.collect { state ->
                updateUI(state)
            }
        }
    }

    private fun updateUI(state: PrayerViewModel.UiState) {
        // Update prayer times
        state.prayerTimes?.let { prayerTimes ->
            prayerTimesAdapter.submitPrayerTimes(prayerTimes, state.prayerLogs)

            // Update Hijri date
            binding.textHijriDate.text = prayerTimes.hijriDate

            // Update location button text
            binding.buttonLocation.text = prayerTimes.city
        }

        // Update current prayer info
        state.nextPrayer?.let { nextPrayer ->
            // Display current prayer prominently
            binding.textCurrentPrayer.text = nextPrayer.prayer.displayName

            // Split time into parts for better display
            val timeParts = nextPrayer.time.split(" ")
            if (timeParts.size == 2) {
                binding.textCurrentPrayerTime.text = timeParts[0]
                binding.textCurrentPrayerPeriod.text = timeParts[1]
            } else {
                binding.textCurrentPrayerTime.text = nextPrayer.time
                binding.textCurrentPrayerPeriod.text = ""
            }

            // Update countdown - this is now handled by the countdown timer
        }

        // Update selected date
        binding.textSelectedDate.text = "Today"

        // Handle error state
        if (state.error != null) {
            // Show error message
            binding.textError.text = state.error
            binding.textError.visibility = View.VISIBLE
        } else {
            binding.textError.visibility = View.GONE
        }

        // Handle navigation
        if (state.navigateToTracker) {
            (activity as? MainActivity)?.navigateToPrayerTracker()
            viewModel.onNavigationHandled()
        }
    }

    private fun initializeLocationAndPrayerTimes() {
        try {
            // Check location permission
            if (locationManager.hasLocationPermission()) {
                loadCurrentLocation()
            } else {
                requestLocationPermission()
            }
        } catch (e: Exception) {
            android.util.Log.e("PrayerFragment", "Error initializing location", e)
            binding.buttonLocation.text = "Select Location"
        }
    }

    private fun requestLocationPermission() {
        locationPermissionLauncher.launch(
            arrayOf(
                Manifest.permission.ACCESS_FINE_LOCATION,
                Manifest.permission.ACCESS_COARSE_LOCATION
            )
        )
    }

    private fun loadCurrentLocation() {
        lifecycleScope.launch {
            try {
                binding.buttonLocation.text = "Detecting..."
                currentLocation = locationManager.getCurrentLocation()

                currentLocation?.let { location ->
                    binding.buttonLocation.text = location.cityName
                    loadPrayerTimes(location)
                } ?: run {
                    // If location detection fails, load default location (Mecca)
                    binding.buttonLocation.text = "Select Location"
                    val defaultLocation = LocationManager.LocationData(
                        21.4225, 39.8262, "Mecca", "Saudi Arabia"
                    )
                    currentLocation = defaultLocation
                    loadPrayerTimes(defaultLocation)
                }
            } catch (e: Exception) {
                binding.buttonLocation.text = "Location Error"
                // Load default location on error
                val defaultLocation = LocationManager.LocationData(
                    21.4225, 39.8262, "Mecca", "Saudi Arabia"
                )
                currentLocation = defaultLocation
                loadPrayerTimes(defaultLocation)
            }
        }
    }

    private fun loadPrayerTimes(location: LocationManager.LocationData) {
        lifecycleScope.launch {
            try {
                currentPrayerTimes = prayerTimeManager.calculatePrayerTimes(
                    location.latitude,
                    location.longitude
                )
                updatePrayerTimesUI()
            } catch (e: Exception) {
                // Handle error
            }
        }
    }

    private fun navigateToLocationPicker() {
        (activity as? MainActivity)?.navigateToLocationPicker()
    }

    private fun navigateToNotificationSettings() {
        (activity as? MainActivity)?.navigateToNotificationSettings()
    }

    private fun startCountdownTimer() {
        countdownHandler = Handler(Looper.getMainLooper())
        updateCountdown()
    }

    private fun updateCountdown() {
        currentPrayerTimes?.let { prayerTimes ->
            val nextPrayer = prayerTimeManager.getNextPrayer(prayerTimes)

            // Update next prayer name
            binding.textNextPrayerName.text = nextPrayer.name

            // Update countdown timer
            val timeUntil = prayerTimeManager.formatTimeUntil(nextPrayer.timeUntil)
            binding.textCountdownTimer.text = timeUntil

            // Schedule next update in 1 second
            countdownRunnable = Runnable { updateCountdown() }
            countdownHandler?.postDelayed(countdownRunnable!!, 1000)
        }
    }

    private fun updatePrayerTimesUI() {
        currentPrayerTimes?.let { managerPrayerTimes ->
            // Update current prayer display
            val now = Calendar.getInstance()
            val timeFormat = SimpleDateFormat("h:mm a", Locale.getDefault())
            val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())

            // Convert manager prayer times to domain entity
            val domainPrayerTimes = PrayerTimes(
                date = dateFormat.format(managerPrayerTimes.date.time),
                fajr = timeFormat.format(managerPrayerTimes.fajr.time),
                sunrise = timeFormat.format(managerPrayerTimes.sunrise.time),
                dhuhr = timeFormat.format(managerPrayerTimes.dhuhr.time),
                asr = timeFormat.format(managerPrayerTimes.asr.time),
                maghrib = timeFormat.format(managerPrayerTimes.maghrib.time),
                isha = timeFormat.format(managerPrayerTimes.isha.time),
                hijriDate = "Dhul-Qidah 29, 1446 AH", // TODO: Calculate actual Hijri date
                city = currentLocation?.cityName ?: "Unknown",
                country = currentLocation?.countryName ?: "Unknown",
                latitude = currentLocation?.latitude ?: 0.0,
                longitude = currentLocation?.longitude ?: 0.0,
                timezone = TimeZone.getDefault().id
            )

            // Update RecyclerView
            prayerTimesAdapter.submitPrayerTimes(domainPrayerTimes, emptyList())

            // Find current prayer
            val prayers = listOf(
                "Fajr" to managerPrayerTimes.fajr,
                "Dhuhr" to managerPrayerTimes.dhuhr,
                "Asr" to managerPrayerTimes.asr,
                "Maghrib" to managerPrayerTimes.maghrib,
                "Isha" to managerPrayerTimes.isha
            )

            var currentPrayerName = "Isha" // Default to last prayer
            var currentPrayerTime = managerPrayerTimes.isha

            for (i in prayers.indices) {
                val (name, time) = prayers[i]
                val nextIndex = if (i + 1 < prayers.size) i + 1 else 0
                val nextTime = if (nextIndex == 0) {
                    // Tomorrow's Fajr
                    val tomorrow = Calendar.getInstance().apply { add(Calendar.DAY_OF_YEAR, 1) }
                    prayerTimeManager.calculatePrayerTimes(
                        currentLocation?.latitude ?: 0.0,
                        currentLocation?.longitude ?: 0.0,
                        tomorrow
                    ).fajr
                } else {
                    prayers[nextIndex].second
                }

                if (now.timeInMillis >= time.timeInMillis && now.timeInMillis < nextTime.timeInMillis) {
                    currentPrayerName = name
                    currentPrayerTime = time
                    break
                }
            }

            // Update current prayer display
            binding.textCurrentPrayer.text = currentPrayerName
            val timeString = timeFormat.format(currentPrayerTime.time)
            val timeParts = timeString.split(" ")
            if (timeParts.size == 2) {
                binding.textCurrentPrayerTime.text = timeParts[0]
                binding.textCurrentPrayerPeriod.text = timeParts[1]
            }

            // Update Hijri date
            binding.textHijriDate.text = domainPrayerTimes.hijriDate
        }
    }

    override fun onResume() {
        super.onResume()
        // Restart countdown timer
        startCountdownTimer()
        // Check if location has changed and reload if needed
        checkAndReloadLocation()
        // Only update next prayer countdown, don't refresh all data
        viewModel.updateNextPrayerCountdown()
    }

    private fun checkAndReloadLocation() {
        lifecycleScope.launch {
            try {
                val savedLocation = locationManager.getCurrentLocation()
                if (savedLocation != null && savedLocation != currentLocation) {
                    // Location has changed, reload prayer times
                    currentLocation = savedLocation
                    binding.buttonLocation.text = savedLocation.cityName
                    loadPrayerTimes(savedLocation)
                }
            } catch (e: Exception) {
                // Handle error silently
            }
        }
    }

    override fun onPause() {
        super.onPause()
        // Stop countdown timer to save battery
        countdownHandler?.removeCallbacks(countdownRunnable ?: return)
    }

    private fun onThemeApplied(themeColors: ThemeManager.ThemeColors) {

        // Apply theme-specific colors to prayer page elements
        binding.apply {
            // Update mosque background based on theme
            val mosqueBackground = if (themeManager.isLightYellowTheme()) {
                androidx.core.content.ContextCompat.getDrawable(requireContext(), com.muslimcore.R.drawable.mosque_background_light)
            } else {
                androidx.core.content.ContextCompat.getDrawable(requireContext(), com.muslimcore.R.drawable.mosque_background)
            }

            // Find the header LinearLayout and apply the mosque background
            headerLayout.background = mosqueBackground

            // Update button colors
            buttonTrackPrayers.backgroundTintList = android.content.res.ColorStateList.valueOf(themeColors.buttonBackgroundColor)
            buttonLocation.backgroundTintList = android.content.res.ColorStateList.valueOf(themeColors.buttonBackgroundColor)

            // Update text colors
            textCurrentPrayer.setTextColor(themeColors.headerTextColor)
            textCurrentPrayerTime.setTextColor(themeColors.headerTextColor)
            textCurrentPrayerPeriod.setTextColor(themeColors.headerTextColor)
            textNextPrayerName.setTextColor(themeColors.headerTextColor)
            textCountdownTimer.setTextColor(themeColors.headerTextColor)
            textHijriDate.setTextColor(themeColors.headerTextColor)
            textSelectedDate.setTextColor(themeColors.textSecondaryColor)

            // Update navigation arrows
            buttonPreviousDay.imageTintList = android.content.res.ColorStateList.valueOf(themeColors.headerTextColor)
            buttonNextDay.imageTintList = android.content.res.ColorStateList.valueOf(themeColors.headerTextColor)
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        // Clean up countdown timer
        countdownHandler?.removeCallbacks(countdownRunnable ?: return)
        countdownHandler = null
        countdownRunnable = null
        _binding = null
    }
}
