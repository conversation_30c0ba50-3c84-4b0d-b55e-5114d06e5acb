<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- Selected state - filled with green -->
    <item android:state_selected="true">
        <layer-list>
            <!-- Background circle for better visibility -->
            <item>
                <shape android:shape="oval">
                    <solid android:color="#204CAF50" />
                    <size android:width="32dp" android:height="32dp" />
                </shape>
            </item>
            <!-- Prayer icon filled with green -->
            <item android:drawable="@drawable/prayer_icon_filled" 
                  android:gravity="center" />
        </layer-list>
    </item>
    
    <!-- Normal state - outline only -->
    <item>
        <layer-list>
            <!-- Prayer icon outline -->
            <item android:drawable="@drawable/prayer_icon" 
                  android:gravity="center" />
        </layer-list>
    </item>
</selector>
