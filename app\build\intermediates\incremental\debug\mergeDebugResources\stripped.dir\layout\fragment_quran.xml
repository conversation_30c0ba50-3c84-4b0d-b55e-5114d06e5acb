<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#0F4A3C"
    android:orientation="vertical">

    <!-- Header -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:padding="16dp">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Quran"
            android:textColor="@android:color/white"
            android:textSize="24sp"
            android:textStyle="bold" />

    </LinearLayout>

    <!-- Search Bar -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="16dp"
        android:layout_marginBottom="16dp"
        android:background="@drawable/search_background"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:padding="12dp">

        <ImageView
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_marginEnd="12dp"
            android:src="@drawable/ic_search"
            android:tint="#80FFFFFF" />

        <EditText
            android:id="@+id/editTextSearch"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:background="@null"
            android:hint="Search"
            android:textColor="@android:color/white"
            android:textColorHint="#80FFFFFF"
            android:textSize="16sp" />

        <ImageView
            android:id="@+id/imageViewMicrophone"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_marginStart="12dp"
            android:src="@drawable/ic_mic"
            android:tint="#80FFFFFF" />

    </LinearLayout>

    <!-- Continue Reading Section -->
    <LinearLayout
        android:id="@+id/layoutContinueReading"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="16dp"
        android:layout_marginBottom="16dp"
        android:background="@drawable/continue_reading_background"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:padding="16dp">

        <ImageView
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginEnd="12dp"
            android:src="@drawable/ic_book"
            android:tint="@android:color/white" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:id="@+id/textContinueReading"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Al-Baqara 2:269"
                android:textColor="@android:color/white"
                android:textSize="16sp"
                android:textStyle="bold" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Continue Reading"
                android:textColor="#80FFFFFF"
                android:textSize="14sp" />

        </LinearLayout>

        <ImageView
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:src="@drawable/ic_arrow_forward"
            android:tint="@android:color/white" />

    </LinearLayout>

    <!-- Tabs -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="16dp"
        android:layout_marginBottom="16dp"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tabSura"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:background="@drawable/tab_selected_background"
            android:gravity="center"
            android:padding="12dp"
            android:text="Sura"
            android:textColor="@android:color/white"
            android:textSize="16sp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tabJuz"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:padding="12dp"
            android:text="Juz"
            android:textColor="#80FFFFFF"
            android:textSize="16sp" />

        <TextView
            android:id="@+id/tabFavorite"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:padding="12dp"
            android:text="Favorite"
            android:textColor="#80FFFFFF"
            android:textSize="16sp" />

    </LinearLayout>

    <!-- Surah List -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerViewSurahs"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:paddingTop="8dp"
        android:paddingBottom="16dp"
        android:clipToPadding="false"
        tools:listitem="@layout/item_surah" />

</LinearLayout>
